#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库初始化脚本
创建数据库表并添加示例数据
"""

import os
import sys
import json
from datetime import datetime, timedelta
import random

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.models import DatabaseManager, get_db_manager

def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    
    # 获取数据库管理器
    db = get_db_manager()
    
    print("数据库表创建完成")
    
    # 添加示例数据
    add_sample_data(db)
    
    print("数据库初始化完成！")
    return db

def add_sample_data(db):
    """添加示例数据"""
    print("添加示例数据...")
    
    # 示例语音识别记录 - 增加更多数据
    sample_speech_records = []

    # 基础命令模板
    commands_template = [
        ('0001-kd', '打开灯', '上传'),
        ('0002-gd', '关闭灯', '录音'),
        ('0003-kds', '打开电视', '自动监听'),
        ('0004-gds', '关闭电视', '录音'),
        ('0005-kkt', '打开空调', '自动监听'),
        ('0002-gd', '关闭空调', '上传'),
    ]

    # 生成30条语音识别记录
    for i in range(30):
        cmd_template = commands_template[i % len(commands_template)]
        predicted_class, command, source = cmd_template

        # 随机生成置信度和音频参数
        confidence = random.uniform(0.75, 0.98)
        audio_length = random.uniform(0.8, 2.5)
        max_val = random.uniform(0.6, 0.95)
        min_val = -random.uniform(0.5, 0.9)
        mean_val = random.uniform(-0.05, 0.05)
        std_val = random.uniform(0.1, 0.2)

        # 偶尔添加一些失败的识别
        success = True
        if random.random() < 0.1:  # 10% 失败率
            success = False
            confidence = random.uniform(0.3, 0.6)

        record = {
            'source': source,
            'predicted_class': predicted_class,
            'command': command,
            'confidence': confidence,
            'audio_length': audio_length,
            'audio_stats': json.dumps({
                'max_value': max_val,
                'min_value': min_val,
                'mean': mean_val,
                'std': std_val,
                'length': int(audio_length * 16000)
            }),
            'success': success
        }
        sample_speech_records.append(record)
    
    # 添加语音识别记录
    speech_record_ids = []
    for i, record in enumerate(sample_speech_records):
        record_id = db.add_speech_record(**record)
        speech_record_ids.append(record_id)
        print(f"添加语音识别记录 {i+1}: {record['command']}")
    
    # 示例设备控制记录 - 增加更多数据
    sample_device_records = []

    # 设备信息
    devices_info = [
        ('light', '灯光'),
        ('tv', '电视'),
        ('ac', '空调')
    ]

    # 生成25条设备控制记录
    current_states = {'light': False, 'tv': False, 'ac': False}

    for i in range(25):
        device_name, device_label = devices_info[i % len(devices_info)]

        # 随机决定是开启还是关闭
        if random.random() < 0.6:  # 60% 概率开启
            action = '开启'
            new_state = True
        else:
            action = '关闭'
            new_state = False

        previous_state = current_states[device_name]
        current_states[device_name] = new_state

        # 关联语音识别记录
        speech_record_id = speech_record_ids[i] if i < len(speech_record_ids) else None

        record = {
            'device_name': device_name,
            'device_label': device_label,
            'action': action,
            'previous_state': previous_state,
            'new_state': new_state,
            'speech_record_id': speech_record_id
        }
        sample_device_records.append(record)
    
    # 添加设备控制记录
    for i, record in enumerate(sample_device_records):
        db.add_device_record(**record)
        print(f"添加设备控制记录 {i+1}: {record['device_label']} {record['action']}")
    
    # 示例系统日志 - 增加更多数据
    sample_logs = []

    # 日志模板
    log_templates = [
        ('INFO', 'GUI', '系统启动成功', '智能家居语音控制系统已成功启动'),
        ('INFO', 'MODEL', '模型加载成功', '哈萨克语语音识别模型加载完成，支持5个类别'),
        ('INFO', 'DATABASE', '数据库连接成功', '成功连接到SQLite数据库'),
        ('INFO', 'AUDIO', '音频处理完成', '成功处理音频文件，提取MFCC特征'),
        ('INFO', 'DEVICE', '设备状态更新', '设备状态已成功更新'),
        ('INFO', 'RECOGNITION', '语音识别成功', '成功识别用户语音命令'),
        ('WARNING', 'AUDIO', '音频质量较低', '检测到音频信噪比较低，可能影响识别准确率'),
        ('WARNING', 'MODEL', '置信度较低', '模型预测置信度低于阈值，建议重新录音'),
        ('WARNING', 'DEVICE', '设备响应延迟', '设备控制响应时间超过预期'),
        ('WARNING', 'SYSTEM', '内存使用率高', '系统内存使用率超过80%'),
        ('ERROR', 'RECOGNITION', '识别失败', '音频特征提取失败，音频长度不足'),
        ('ERROR', 'AUDIO', '音频读取失败', '无法读取音频文件，文件可能损坏'),
        ('ERROR', 'MODEL', '模型预测错误', '模型预测过程中发生异常'),
        ('ERROR', 'DEVICE', '设备控制失败', '无法连接到设备，请检查设备状态'),
        ('ERROR', 'DATABASE', '数据库写入失败', '数据库操作失败，请检查数据库连接'),
    ]

    # 生成40条系统日志
    for i in range(40):
        template = log_templates[i % len(log_templates)]
        level, module, message, details = template

        # 为某些日志添加随机变化
        if '成功' in message and random.random() < 0.3:
            details += f' (耗时: {random.randint(50, 500)}ms)'
        elif '失败' in message and random.random() < 0.4:
            details += f' (错误代码: {random.randint(1001, 9999)})'

        log = {
            'level': level,
            'module': module,
            'message': message,
            'details': details
        }
        sample_logs.append(log)
    
    # 添加系统日志
    for i, log in enumerate(sample_logs):
        db.add_system_log(**log)
        print(f"添加系统日志 {i+1}: {log['level']} - {log['message']}")
    
    # 示例用户操作记录 - 增加更多数据
    sample_operations = []

    # 操作模板
    operation_templates = [
        ('启动系统', '用户启动智能家居语音控制系统', 'SUCCESS'),
        ('关闭系统', '用户关闭智能家居语音控制系统', 'SUCCESS'),
        ('上传音频', '用户上传音频文件进行识别', 'SUCCESS'),
        ('开始录音', '用户开始录音进行语音识别', 'SUCCESS'),
        ('停止录音', '用户停止录音', 'SUCCESS'),
        ('自动监听', '用户开启自动语音监听功能', 'SUCCESS'),
        ('停止自动监听', '用户停止自动语音监听功能', 'SUCCESS'),
        ('查看数据库', '用户打开数据库查看器', 'SUCCESS'),
        ('刷新界面', '用户刷新系统界面', 'SUCCESS'),
        ('调整设置', '用户修改系统设置', 'SUCCESS'),
        ('导出数据', '用户导出系统数据', 'SUCCESS'),
        ('清空数据', '用户清空历史数据', 'SUCCESS'),
        ('模型重载', '用户重新加载识别模型', 'SUCCESS'),
        ('音频测试', '用户进行音频设备测试', 'SUCCESS'),
        ('系统诊断', '用户运行系统诊断工具', 'SUCCESS'),
    ]

    # 生成35条用户操作记录
    for i in range(35):
        template = operation_templates[i % len(operation_templates)]
        operation_type, operation_details, result = template

        # 偶尔添加一些失败的操作
        error_message = None
        if random.random() < 0.05:  # 5% 失败率
            result = 'FAILED'
            error_messages = [
                '网络连接超时',
                '文件权限不足',
                '内存不足',
                '设备忙碌',
                '参数错误'
            ]
            error_message = random.choice(error_messages)
            operation_details += f' (失败原因: {error_message})'

        # 为某些操作添加更多细节
        if '音频' in operation_type and random.random() < 0.4:
            file_types = ['wav', 'mp3', 'm4a']
            file_type = random.choice(file_types)
            operation_details += f' (文件类型: {file_type})'

        operation = {
            'operation_type': operation_type,
            'operation_details': operation_details,
            'result': result,
            'error_message': error_message if result == 'FAILED' else None
        }
        sample_operations.append(operation)
    
    # 添加用户操作记录
    for i, operation in enumerate(sample_operations):
        db.add_user_operation(**operation)
        print(f"添加用户操作记录 {i+1}: {operation['operation_type']}")
    
    print(f"示例数据添加完成！")
    print(f"- 语音识别记录: {len(sample_speech_records)} 条")
    print(f"- 设备控制记录: {len(sample_device_records)} 条")
    print(f"- 系统日志: {len(sample_logs)} 条")
    print(f"- 用户操作记录: {len(sample_operations)} 条")
    print(f"总计: {len(sample_speech_records) + len(sample_device_records) + len(sample_logs) + len(sample_operations)} 条记录")

def show_database_stats(db):
    """显示数据库统计信息"""
    print("\n=== 数据库统计信息 ===")
    
    # 语音识别统计
    recent_speech = db.get_recent_speech_records(5)
    print(f"最近5条语音识别记录:")
    for record in recent_speech:
        print(f"  - {record.timestamp.strftime('%Y-%m-%d %H:%M:%S')}: {record.command} (置信度: {record.confidence:.2f})")
    
    # 设备使用统计
    device_stats = db.get_device_statistics()
    print(f"\n设备使用统计:")
    device_names = {'light': '灯光', 'tv': '电视', 'ac': '空调'}
    for device, count in device_stats.items():
        print(f"  - {device_names.get(device, device)}: {count} 次操作")
    
    # 识别准确率
    accuracy = db.get_recognition_accuracy()
    print(f"\n语音识别准确率: {accuracy:.1f}%")

if __name__ == "__main__":
    # 初始化数据库
    db = init_database()
    
    # 显示统计信息
    show_database_stats(db)
    
    # 关闭数据库连接
    db.close()
