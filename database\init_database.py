#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库初始化脚本
创建数据库表并添加示例数据
"""

import os
import sys
import json
from datetime import datetime, timedelta
import random

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.models import DatabaseManager, get_db_manager

def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    
    # 获取数据库管理器
    db = get_db_manager()
    
    print("数据库表创建完成")
    
    # 添加示例数据
    add_sample_data(db)
    
    print("数据库初始化完成！")
    return db

def add_sample_data(db):
    """添加示例数据"""
    print("添加示例数据...")
    
    # 示例语音识别记录
    sample_speech_records = [
        {
            'source': '上传',
            'predicted_class': '0001-kd',
            'command': '打开灯',
            'confidence': 0.95,
            'audio_length': 1.2,
            'audio_stats': json.dumps({
                'max_value': 0.8,
                'min_value': -0.7,
                'mean': 0.02,
                'std': 0.15
            }),
            'success': True
        },
        {
            'source': '录音',
            'predicted_class': '0002-gd',
            'command': '关闭灯',
            'confidence': 0.88,
            'audio_length': 1.5,
            'audio_stats': json.dumps({
                'max_value': 0.6,
                'min_value': -0.5,
                'mean': 0.01,
                'std': 0.12
            }),
            'success': True
        },
        {
            'source': '自动监听',
            'predicted_class': '0003-kds',
            'command': '打开电视',
            'confidence': 0.92,
            'audio_length': 1.8,
            'audio_stats': json.dumps({
                'max_value': 0.9,
                'min_value': -0.8,
                'mean': 0.03,
                'std': 0.18
            }),
            'success': True
        },
        {
            'source': '录音',
            'predicted_class': '0004-gds',
            'command': '关闭电视',
            'confidence': 0.85,
            'audio_length': 1.3,
            'audio_stats': json.dumps({
                'max_value': 0.7,
                'min_value': -0.6,
                'mean': 0.02,
                'std': 0.14
            }),
            'success': True
        },
        {
            'source': '自动监听',
            'predicted_class': '0005-kkt',
            'command': '打开空调',
            'confidence': 0.90,
            'audio_length': 2.1,
            'audio_stats': json.dumps({
                'max_value': 0.85,
                'min_value': -0.75,
                'mean': 0.04,
                'std': 0.16
            }),
            'success': True
        }
    ]
    
    # 添加语音识别记录
    speech_record_ids = []
    for i, record in enumerate(sample_speech_records):
        record_id = db.add_speech_record(**record)
        speech_record_ids.append(record_id)
        print(f"添加语音识别记录 {i+1}: {record['command']}")
    
    # 示例设备控制记录
    sample_device_records = [
        {
            'device_name': 'light',
            'device_label': '灯光',
            'action': '开启',
            'previous_state': False,
            'new_state': True,
            'speech_record_id': speech_record_ids[0] if speech_record_ids else None
        },
        {
            'device_name': 'light',
            'device_label': '灯光',
            'action': '关闭',
            'previous_state': True,
            'new_state': False,
            'speech_record_id': speech_record_ids[1] if len(speech_record_ids) > 1 else None
        },
        {
            'device_name': 'tv',
            'device_label': '电视',
            'action': '开启',
            'previous_state': False,
            'new_state': True,
            'speech_record_id': speech_record_ids[2] if len(speech_record_ids) > 2 else None
        },
        {
            'device_name': 'tv',
            'device_label': '电视',
            'action': '关闭',
            'previous_state': True,
            'new_state': False,
            'speech_record_id': speech_record_ids[3] if len(speech_record_ids) > 3 else None
        },
        {
            'device_name': 'ac',
            'device_label': '空调',
            'action': '开启',
            'previous_state': False,
            'new_state': True,
            'speech_record_id': speech_record_ids[4] if len(speech_record_ids) > 4 else None
        }
    ]
    
    # 添加设备控制记录
    for i, record in enumerate(sample_device_records):
        db.add_device_record(**record)
        print(f"添加设备控制记录 {i+1}: {record['device_label']} {record['action']}")
    
    # 示例系统日志
    sample_logs = [
        {
            'level': 'INFO',
            'module': 'GUI',
            'message': '系统启动成功',
            'details': '智能家居语音控制系统已成功启动'
        },
        {
            'level': 'INFO',
            'module': 'MODEL',
            'message': '模型加载成功',
            'details': '哈萨克语语音识别模型加载完成，支持5个类别'
        },
        {
            'level': 'WARNING',
            'module': 'AUDIO',
            'message': '音频质量较低',
            'details': '检测到音频信噪比较低，可能影响识别准确率'
        },
        {
            'level': 'INFO',
            'module': 'DATABASE',
            'message': '数据库连接成功',
            'details': '成功连接到SQLite数据库'
        },
        {
            'level': 'ERROR',
            'module': 'RECOGNITION',
            'message': '识别失败',
            'details': '音频特征提取失败，音频长度不足'
        }
    ]
    
    # 添加系统日志
    for i, log in enumerate(sample_logs):
        db.add_system_log(**log)
        print(f"添加系统日志 {i+1}: {log['level']} - {log['message']}")
    
    # 示例用户操作记录
    sample_operations = [
        {
            'operation_type': '启动系统',
            'operation_details': '用户启动智能家居语音控制系统',
            'result': 'SUCCESS'
        },
        {
            'operation_type': '上传音频',
            'operation_details': '用户上传音频文件进行识别',
            'result': 'SUCCESS'
        },
        {
            'operation_type': '开始录音',
            'operation_details': '用户开始录音进行语音识别',
            'result': 'SUCCESS'
        },
        {
            'operation_type': '自动监听',
            'operation_details': '用户开启自动语音监听功能',
            'result': 'SUCCESS'
        },
        {
            'operation_type': '停止录音',
            'operation_details': '用户停止录音',
            'result': 'SUCCESS'
        }
    ]
    
    # 添加用户操作记录
    for i, operation in enumerate(sample_operations):
        db.add_user_operation(**operation)
        print(f"添加用户操作记录 {i+1}: {operation['operation_type']}")
    
    print(f"示例数据添加完成！")
    print(f"- 语音识别记录: {len(sample_speech_records)} 条")
    print(f"- 设备控制记录: {len(sample_device_records)} 条")
    print(f"- 系统日志: {len(sample_logs)} 条")
    print(f"- 用户操作记录: {len(sample_operations)} 条")

def show_database_stats(db):
    """显示数据库统计信息"""
    print("\n=== 数据库统计信息 ===")
    
    # 语音识别统计
    recent_speech = db.get_recent_speech_records(5)
    print(f"最近5条语音识别记录:")
    for record in recent_speech:
        print(f"  - {record.timestamp.strftime('%Y-%m-%d %H:%M:%S')}: {record.command} (置信度: {record.confidence:.2f})")
    
    # 设备使用统计
    device_stats = db.get_device_statistics()
    print(f"\n设备使用统计:")
    device_names = {'light': '灯光', 'tv': '电视', 'ac': '空调'}
    for device, count in device_stats.items():
        print(f"  - {device_names.get(device, device)}: {count} 次操作")
    
    # 识别准确率
    accuracy = db.get_recognition_accuracy()
    print(f"\n语音识别准确率: {accuracy:.1f}%")

if __name__ == "__main__":
    # 初始化数据库
    db = init_database()
    
    # 显示统计信息
    show_database_stats(db)
    
    # 关闭数据库连接
    db.close()
