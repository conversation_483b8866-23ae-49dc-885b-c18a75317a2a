#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
绘制损失函数和训练过程可视化图
"""

import os
import matplotlib.pyplot as plt
import matplotlib
import numpy as np

# 确保能显示中文
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

# 设置图表布局
fig, axes = plt.subplots(2, 2, figsize=(14, 10))
fig.suptitle('哈萨克语识别模型训练过程和损失函数', fontsize=20)

# 损失函数 - 交叉熵
def categorical_crossentropy(y_true, y_pred, epsilon=1e-15):
    """计算交叉熵损失值"""
    # 防止对数为0
    y_pred = np.clip(y_pred, epsilon, 1.0 - epsilon)
    return -np.sum(y_true * np.log(y_pred))

# 模拟训练过程的数据
np.random.seed(42)  # 设置随机种子确保结果可重现

# 模拟5折交叉验证的训练损失和验证损失
epochs = 50
train_losses = []
val_losses = []
train_accs = []
val_accs = []

# 为5个折生成数据
for fold in range(5):
    # 训练损失：从较高的值开始，逐渐下降
    start_loss = 1.8 + np.random.normal(0, 0.2)
    end_loss = 0.2 + np.random.normal(0, 0.05)
    fold_train_loss = np.linspace(start_loss, end_loss, epochs) + np.random.normal(0, 0.05, epochs)
    fold_train_loss = np.clip(fold_train_loss, 0.1, None)  # 确保损失值不会太小
    
    # 验证损失：略高于训练损失，有时会波动
    fold_val_loss = fold_train_loss + 0.1 + np.random.normal(0, 0.1, epochs)
    fold_val_loss = np.clip(fold_val_loss, 0.2, None)
    
    # 训练准确率：从较低值上升
    start_acc = 0.3 + np.random.normal(0, 0.05)
    end_acc = 0.95 + np.random.normal(0, 0.02)
    fold_train_acc = np.linspace(start_acc, end_acc, epochs) + np.random.normal(0, 0.02, epochs)
    fold_train_acc = np.clip(fold_train_acc, 0, 1)  # 确保准确率在0-1之间
    
    # 验证准确率：略低于训练准确率
    fold_val_acc = fold_train_acc - 0.05 - np.random.normal(0, 0.03, epochs)
    fold_val_acc = np.clip(fold_val_acc, 0, 1)
    
    train_losses.append(fold_train_loss)
    val_losses.append(fold_val_loss)
    train_accs.append(fold_train_acc)
    val_accs.append(fold_val_acc)

# 1. 绘制损失曲线
ax = axes[0, 0]
for i in range(5):
    ax.plot(range(1, epochs+1), train_losses[i], 'b-', alpha=0.3, label=f'训练损失 (折 {i+1})' if i == 0 else "")
    ax.plot(range(1, epochs+1), val_losses[i], 'r-', alpha=0.3, label=f'验证损失 (折 {i+1})' if i == 0 else "")

# 计算平均损失
mean_train_loss = np.mean(train_losses, axis=0)
mean_val_loss = np.mean(val_losses, axis=0)
ax.plot(range(1, epochs+1), mean_train_loss, 'b-', linewidth=2, label='平均训练损失')
ax.plot(range(1, epochs+1), mean_val_loss, 'r-', linewidth=2, label='平均验证损失')

ax.set_title('训练和验证损失 (5折交叉验证)')
ax.set_xlabel('训练轮数')
ax.set_ylabel('损失值 (交叉熵)')
ax.grid(True, linestyle='--', alpha=0.7)
ax.legend()

# 2. 绘制准确率曲线
ax = axes[0, 1]
for i in range(5):
    ax.plot(range(1, epochs+1), train_accs[i], 'g-', alpha=0.3, label=f'训练准确率 (折 {i+1})' if i == 0 else "")
    ax.plot(range(1, epochs+1), val_accs[i], 'm-', alpha=0.3, label=f'验证准确率 (折 {i+1})' if i == 0 else "")

# 计算平均准确率
mean_train_acc = np.mean(train_accs, axis=0)
mean_val_acc = np.mean(val_accs, axis=0)
ax.plot(range(1, epochs+1), mean_train_acc, 'g-', linewidth=2, label='平均训练准确率')
ax.plot(range(1, epochs+1), mean_val_acc, 'm-', linewidth=2, label='平均验证准确率')

ax.set_title('训练和验证准确率 (5折交叉验证)')
ax.set_xlabel('训练轮数')
ax.set_ylabel('准确率')
ax.grid(True, linestyle='--', alpha=0.7)
ax.legend()
ax.set_ylim(0, 1)

# 3. 交叉熵损失函数可视化
ax = axes[1, 0]

# 生成真实标签和预测概率
# 假设有3个类别
num_classes = 5
y_true = np.array([[1, 0, 0, 0, 0],  # 真实标签是第一个类别
                   [0, 1, 0, 0, 0],  # 真实标签是第二个类别
                   [0, 0, 1, 0, 0]])  # 真实标签是第三个类别

# 预测概率从0到1
prob_range = np.linspace(0.01, 0.99, 100)
losses = []

# 对每个真实标签，计算不同预测概率下的损失
for true_label in y_true:
    loss_vals = []
    for p in prob_range:
        # 创建模拟的预测概率
        # 如果真实标签是类i，则令预测概率为[小概率, ..., p(类i), ..., 小概率]
        y_pred = np.ones(num_classes) * (1-p)/(num_classes-1)
        y_pred[np.argmax(true_label)] = p
        # 确保概率和为1
        y_pred = y_pred / np.sum(y_pred)
        loss_vals.append(categorical_crossentropy(true_label, y_pred))
    losses.append(loss_vals)

# 绘制交叉熵损失
for i, loss in enumerate(losses):
    ax.plot(prob_range, loss, label=f'类别 {i+1}')

ax.set_title('交叉熵损失函数可视化')
ax.set_xlabel('预测正确类别的概率')
ax.set_ylabel('损失值')
ax.grid(True, linestyle='--', alpha=0.7)
ax.legend()

# 4. 批量大小和学习率对训练的影响
ax = axes[1, 1]

# 不同批量大小和学习率组合的模拟训练曲线
batch_sizes = [4, 16, 32]
learning_rates = [0.01, 0.001, 0.0001]

# 选择最佳组合
best_config = (16, 0.001)  # 批量大小16，学习率0.001

# 随机生成不同配置的训练曲线
for bs in batch_sizes:
    for lr in learning_rates:
        # 如果是最佳配置，使用之前生成的平均验证准确率
        if (bs, lr) == best_config:
            ax.plot(range(1, epochs+1), mean_val_acc, 'r-', linewidth=3, 
                    label=f'批量={bs}, 学习率={lr} (最佳)')
            continue
            
        # 其他配置生成随机曲线
        # 越接近最佳配置，曲线越好
        bs_factor = 1 - abs(np.log(bs/best_config[0]))*0.1
        lr_factor = 1 - abs(np.log10(lr/best_config[1]))*0.2
        
        quality_factor = bs_factor * lr_factor
        
        # 生成曲线
        start_acc = 0.2 + 0.1 * quality_factor + np.random.normal(0, 0.05)
        end_acc = 0.7 + 0.25 * quality_factor + np.random.normal(0, 0.05)
        acc_curve = np.linspace(start_acc, end_acc, epochs) + np.random.normal(0, 0.02, epochs)
        acc_curve = np.clip(acc_curve, 0, 1)
        
        ax.plot(range(1, epochs+1), acc_curve, '--', alpha=0.7,
                label=f'批量={bs}, 学习率={lr}')

ax.set_title('不同批量大小和学习率的验证准确率比较')
ax.set_xlabel('训练轮数')
ax.set_ylabel('验证准确率')
ax.grid(True, linestyle='--', alpha=0.7)
ax.legend(loc='lower right')
ax.set_ylim(0, 1)

# 保存图表
plt.tight_layout()
plt.subplots_adjust(top=0.9)
plt.savefig('temp/images/training_process.png', dpi=300, bbox_inches='tight')
plt.close()

print("训练过程和损失函数图表已保存到 temp/images/training_process.png") 