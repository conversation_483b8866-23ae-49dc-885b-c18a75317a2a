#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
哈萨克语语音识别模型 - 小型版本
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping

class KazakhSpeechModel:
    """哈萨克语语音识别模型类 - 小型版本"""

    def __init__(self, input_shape, num_classes, model_path=None):
        """
        初始化模型

        参数:
            input_shape (tuple): 输入特征形状
            num_classes (int): 类别数量
            model_path (str, optional): 预训练模型路径
        """
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.model = None

        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            self.build_model()

    def build_model(self):
        """构建小型模型架构"""
        model = models.Sequential()

        # 打印输入形状
        print(f"构建小型模型，输入形状: {self.input_shape}")

        # 第一个卷积块 - 使用较小的卷积核和较少的滤波器
        model.add(layers.Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=self.input_shape))
        model.add(layers.BatchNormalization())
        model.add(layers.MaxPooling2D((2, 2), padding='same'))
        model.add(layers.Dropout(0.2))

        # 第二个卷积块
        model.add(layers.Conv2D(64, (3, 3), activation='relu', padding='same'))
        model.add(layers.BatchNormalization())
        model.add(layers.MaxPooling2D((2, 2), padding='same'))
        model.add(layers.Dropout(0.3))

        # 展平层
        model.add(layers.Flatten())

        # 全连接层 - 使用较少的神经元
        model.add(layers.Dense(128, activation='relu'))
        model.add(layers.BatchNormalization())
        model.add(layers.Dropout(0.5))
        model.add(layers.Dense(self.num_classes, activation='softmax'))

        # 打印模型摘要
        model.summary()

        # 编译模型
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        self.model = model
        return model

    def train(self, X_train, y_train, X_val, y_val, batch_size=32, epochs=50, callbacks=None, save_path=None):
        """
        训练模型

        参数:
            X_train (numpy.ndarray): 训练特征
            y_train (numpy.ndarray): 训练标签
            X_val (numpy.ndarray): 验证特征
            y_val (numpy.ndarray): 验证标签
            batch_size (int): 批量大小
            epochs (int): 训练轮数
            callbacks (list, optional): 回调函数列表
            save_path (str, optional): 模型保存路径

        返回:
            History: 训练历史
        """
        if callbacks is None:
            callbacks = []

            # 添加早停回调 - 增加耐心值，确保模型有足够的训练轮数
            early_stopping = EarlyStopping(
                monitor='val_loss',
                patience=20,  # 增加耐心值，允许模型训练更长时间
                restore_best_weights=True
            )
            callbacks.append(early_stopping)

            # 添加模型检查点回调
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                checkpoint = ModelCheckpoint(
                    save_path,
                    monitor='val_accuracy',
                    save_best_only=True,
                    verbose=1
                )
                callbacks.append(checkpoint)

        # 训练模型
        history = self.model.fit(
            X_train, y_train,
            batch_size=batch_size,
            epochs=epochs,
            validation_data=(X_val, y_val),
            callbacks=callbacks
        )

        return history

    def predict(self, X):
        """
        预测类别

        参数:
            X (numpy.ndarray): 输入特征

        返回:
            numpy.ndarray: 预测结果
        """
        return self.model.predict(X)

    def evaluate(self, X_test, y_test):
        """
        评估模型

        参数:
            X_test (numpy.ndarray): 测试特征
            y_test (numpy.ndarray): 测试标签

        返回:
            tuple: (损失, 准确率)
        """
        return self.model.evaluate(X_test, y_test)

    def save_model(self, save_path):
        """
        保存模型

        参数:
            save_path (str): 保存路径
        """
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        self.model.save(save_path)

    def load_model(self, model_path):
        """
        加载模型

        参数:
            model_path (str): 模型路径
        """
        self.model = models.load_model(model_path)
