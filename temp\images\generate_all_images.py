#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成所有可视化图表并解决中文显示问题
"""

import os
import sys
import subprocess
import platform

def setup_chinese_fonts():
    """
    尝试解决中文显示问题
    """
    print("设置中文字体支持...")
    
    # 检测操作系统
    system = platform.system()
    print(f"操作系统: {system}")
    
    try:
        import matplotlib
        import matplotlib.font_manager as fm
        
        # 配置matplotlib使用支持中文的字体
        if system == 'Windows':
            # Windows常见中文字体
            chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'NSimSun', 'FangSong', 'KaiTi']
        elif system == 'Darwin':  # macOS
            chinese_fonts = ['Arial Unicode MS', 'Heiti TC', 'STHeiti', 'Hiragino Sans GB']
        else:  # Linux
            chinese_fonts = ['WenQuanYi Zen Hei', 'WenQuanYi Micro Hei', 'Droid Sans Fallback']
            
        # 查找可用的中文字体
        found_font = None
        for font in chinese_fonts:
            try:
                test = fm.findfont(font)
                if test and os.path.exists(test) and font not in ['DejaVu Sans']:
                    found_font = font
                    print(f"找到可用的中文字体: {font}")
                    break
            except Exception as e:
                print(f"尝试加载字体 {font} 失败: {e}")
        
        if found_font:
            print(f"设置 matplotlib 使用字体: {found_font}")
            matplotlib.rcParams['font.sans-serif'] = [found_font] + chinese_fonts + ['sans-serif']
            matplotlib.rcParams['axes.unicode_minus'] = False
        else:
            print("警告: 未找到支持中文的字体，可能无法正确显示中文")
            
    except Exception as e:
        print(f"设置中文字体时出错: {e}")

def run_script(script_path):
    """
    运行指定的Python脚本
    """
    print(f"\n运行脚本: {script_path}")
    try:
        # 使用当前Python解释器运行脚本
        subprocess.run([sys.executable, script_path], check=True)
        print(f"脚本 {script_path} 执行成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"脚本 {script_path} 执行失败: {e}")
        return False
    except Exception as e:
        print(f"运行脚本时发生错误: {e}")
        return False

def main():
    """
    主函数 - 生成所有可视化图表
    """
    print("开始生成所有可视化图表...")
    
    # 设置中文字体
    setup_chinese_fonts()
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 要运行的脚本列表
    scripts = [
        os.path.join(current_dir, "system_architecture.py"),
        os.path.join(current_dir, "neural_network_structure.py"),
        os.path.join(current_dir, "data_flow.py"),
        os.path.join(current_dir, "loss_function.py"),
        os.path.join(current_dir, "gui_preview.py")
    ]
    
    # 运行所有脚本并计数成功和失败
    success_count = 0
    fail_count = 0
    
    for script in scripts:
        if os.path.exists(script):
            if run_script(script):
                success_count += 1
            else:
                fail_count += 1
        else:
            print(f"脚本不存在: {script}")
            fail_count += 1
    
    # 打印执行结果汇总
    print("\n执行结果汇总:")
    print(f"成功: {success_count} 脚本")
    print(f"失败: {fail_count} 脚本")
    
    # 打印生成的图片文件
    print("\n生成的图像文件:")
    image_dir = os.path.dirname(current_dir)
    for root, dirs, files in os.walk(image_dir):
        for file in files:
            if file.endswith(('.png', '.jpg', '.jpeg')):
                print(os.path.join(root, file))
    
    print("\n所有图表生成完成!")

if __name__ == "__main__":
    main() 