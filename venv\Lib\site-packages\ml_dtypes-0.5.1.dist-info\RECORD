ml_dtypes-0.5.1.dist-info/AUTHORS,sha256=TfC_uri1JQbY7f7cFmqZJjS7al1_zaHUF7z2qxJVHeg,308
ml_dtypes-0.5.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
ml_dtypes-0.5.1.dist-info/LICENSE,sha256=Pd-b5cKP4n2tFDpdx27qJSIq0d1ok0oEcGTlbtL6QMU,11560
ml_dtypes-0.5.1.dist-info/LICENSE.eigen,sha256=kiHC-TYVm4RG0ykkn7TA8lvlEPRHODoPEzNqx5hWaKM,17099
ml_dtypes-0.5.1.dist-info/METADATA,sha256=gCyodc5lMFxVHAVmxdBXRVKRjqP8L7m-S9S4lBvPZqs,22283
ml_dtypes-0.5.1.dist-info/RECORD,,
ml_dtypes-0.5.1.dist-info/WHEEL,sha256=ZbysKQ-KbiarXzvle8R5OReN2gVclmU0Mb8tCTB42Ak,101
ml_dtypes-0.5.1.dist-info/top_level.txt,sha256=meeeNkM1LLmTU5q_0ssFs21A_42VAoES24ntCrPqASw,10
ml_dtypes/__init__.py,sha256=_zKN4LYRqscwGQ2-5QqWe9xwBXMvw7okGwSgDehYCUM,2478
ml_dtypes/__pycache__/__init__.cpython-310.pyc,,
ml_dtypes/__pycache__/_finfo.cpython-310.pyc,,
ml_dtypes/__pycache__/_iinfo.cpython-310.pyc,,
ml_dtypes/_finfo.py,sha256=JWqJABIHDtgVFaxoy5dkmCRmwbQYdahH-CqA-xH1kos,23612
ml_dtypes/_iinfo.py,sha256=il9ONlgDbzJeV2vVlnSRhwut4WQ7LxXmsYWNTgiL4-o,2100
ml_dtypes/_ml_dtypes_ext.cp310-win_amd64.pyd,sha256=gKiUx1Y5Gdap4dHbNoX4stG5ZOgLTI5fXhlDNUf8Jao,779776
ml_dtypes/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
