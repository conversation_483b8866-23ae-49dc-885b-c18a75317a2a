#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MFCC特征提取模块
"""

import numpy as np
import librosa
import librosa.display
import matplotlib.pyplot as plt

class MFCCExtractor:
    """MFCC特征提取类"""
    
    def __init__(self, sample_rate=16000, n_mfcc=40, n_fft=512, hop_length=160):
        """
        初始化MFCC特征提取器
        
        参数:
            sample_rate (int): 采样率
            n_mfcc (int): MFCC系数数量
            n_fft (int): FFT窗口大小
            hop_length (int): 帧移
        """
        self.sample_rate = sample_rate
        self.n_mfcc = n_mfcc
        self.n_fft = n_fft
        self.hop_length = hop_length
    
    def extract(self, audio_data):
        """
        从音频数据中提取MFCC特征
        
        参数:
            audio_data (numpy.ndarray): 音频数据
            
        返回:
            numpy.ndarray: MFCC特征
        """
        try:
            # 提取MFCC特征
            mfccs = librosa.feature.mfcc(
                y=audio_data, 
                sr=self.sample_rate,
                n_mfcc=self.n_mfcc,
                n_fft=self.n_fft,
                hop_length=self.hop_length
            )
            
            # 标准化MFCC特征
            mfccs = (mfccs - np.mean(mfccs)) / np.std(mfccs)
            
            return mfccs
        except Exception as e:
            print(f"提取MFCC特征时出错: {e}")
            return None
    
    def extract_from_audio(self, audio_data):
        """
        从音频数据中提取MFCC特征 - 重载方法，用于GUI调用
        
        参数:
            audio_data (numpy.ndarray): 音频数据
            
        返回:
            numpy.ndarray: MFCC特征
        """
        return self.extract(audio_data)
    
    def extract_from_file(self, file_path):
        """
        从音频文件中提取MFCC特征
        
        参数:
            file_path (str): 音频文件路径
            
        返回:
            numpy.ndarray: MFCC特征
        """
        try:
            # 加载音频文件
            y, sr = librosa.load(file_path, sr=self.sample_rate)
            
            # 提取MFCC特征
            return self.extract(y)
        except Exception as e:
            print(f"从文件提取MFCC特征时出错: {e}")
            return None
    
    def visualize_mfcc(self, mfccs, output_path=None):
        """
        可视化MFCC特征
        
        参数:
            mfccs (numpy.ndarray): MFCC特征
            output_path (str, optional): 输出图像路径
        """
        plt.figure(figsize=(10, 4))
        librosa.display.specshow(
            mfccs, 
            x_axis='time',
            sr=self.sample_rate,
            hop_length=self.hop_length
        )
        plt.colorbar(format='%+2.0f dB')
        plt.title('MFCC')
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path)
            plt.close()
        else:
            plt.show()
