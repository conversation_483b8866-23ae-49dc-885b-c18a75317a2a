#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
音频处理模块，负责加载和处理音频文件
"""

import os
import numpy as np
import soundfile as sf
import librosa

class AudioProcessor:
    """音频处理类，用于加载和处理音频文件"""

    def __init__(self, sample_rate=16000):
        """
        初始化音频处理器

        参数:
            sample_rate (int): 采样率，默认16000Hz
        """
        self.sample_rate = sample_rate

    def load_audio_file(self, file_path):
        """
        加载音频文件

        参数:
            file_path (str): 音频文件路径

        返回:
            numpy.ndarray: 音频数据，如果加载失败则返回None
        """
        try:
            y, sr = librosa.load(file_path, sr=self.sample_rate)
            return y
        except Exception as e:
            print(f"无法加载音频文件 {file_path}: {e}")
            return None

    def load_audio(self, file_path):
        """
        加载音频文件并返回音频数据和采样率
        
        参数:
            file_path (str): 音频文件路径
            
        返回:
            tuple: (音频数据, 采样率)
        """
        try:
            y, sr = librosa.load(file_path, sr=self.sample_rate)
            return y, sr
        except Exception as e:
            print(f"无法加载音频文件 {file_path}: {e}")
            return None, None

    def save_audio_file(self, data, file_path):
        """
        保存音频文件

        参数:
            data (numpy.ndarray): 音频数据
            file_path (str): 保存路径

        返回:
            bool: 是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)

            # 保存音频文件
            sf.write(file_path, data, self.sample_rate)
            return True
        except Exception as e:
            print(f"保存音频文件失败: {e}")
            return False

    def trim_silence(self, audio_data, threshold=0.03):
        """
        修剪静音

        参数:
            audio_data (numpy.ndarray): 音频数据
            threshold (float): 阈值

        返回:
            numpy.ndarray: 修剪后的音频数据
        """
        try:
            trimmed, _ = librosa.effects.trim(audio_data, top_db=20)
            return trimmed
        except Exception as e:
            print(f"修剪静音失败: {e}")
            return audio_data

    def normalize(self, audio_data):
        """
        归一化音频

        参数:
            audio_data (numpy.ndarray): 音频数据

        返回:
            numpy.ndarray: 归一化后的音频数据
        """
        try:
            return librosa.util.normalize(audio_data)
        except Exception as e:
            print(f"归一化音频失败: {e}")
            return audio_data
