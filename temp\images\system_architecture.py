#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
绘制哈萨克语语音识别系统的架构图
"""

import os
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib
import numpy as np

# 确保能显示中文
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

# 创建图表
plt.figure(figsize=(12, 8))
plt.title('哈萨克语语音识别系统架构图', fontsize=20)

# 定义颜色方案
colors = {
    'audio': '#3498db',
    'feature': '#2ecc71',
    'model': '#9b59b6',
    'control': '#e74c3c',
    'gui': '#f39c12',
    'arrow': '#7f8c8d'
}

# 定义箭头样式
arrow_props = dict(arrowstyle='->', connectionstyle='arc3,rad=0.1', 
                  color=colors['arrow'], linewidth=2)

# 创建模块
def create_box(x, y, width, height, title, details=None, color='#3498db'):
    rect = patches.Rectangle((x, y), width, height, linewidth=2, 
                            edgecolor='black', facecolor=color, alpha=0.7)
    plt.gca().add_patch(rect)
    plt.text(x + width/2, y + height - 0.2, title, 
            ha='center', va='center', fontsize=14, fontweight='bold')
    
    if details:
        detail_y = y + height - 0.5
        for detail in details:
            detail_y -= 0.4
            plt.text(x + width/2, detail_y, detail, ha='center', va='center', fontsize=10)

# 音频处理模块
create_box(1, 6, 2, 2.5, '音频处理模块', 
          ['麦克风录音', '预处理', '降噪', '静音去除'], 
          colors['audio'])

# 特征提取模块
create_box(5, 6, 2, 2.5, '特征提取模块', 
          ['MFCC特征', 'Delta特征', '特征增强', 'SpecAugment'], 
          colors['feature'])

# 模型模块
create_box(9, 6, 2, 2.5, '深度学习模型', 
          ['Transformer架构', '多头注意力', '位置编码', '交叉验证'], 
          colors['model'])

# 设备控制模块
create_box(5, 1, 2, 2.5, '设备控制模块', 
          ['灯光控制', '电视控制', '空调控制', '命令映射'], 
          colors['control'])

# GUI模块
create_box(9, 1, 2, 2.5, 'GUI界面模块', 
          ['3D房间显示', '音频可视化', '实时波形', '设备状态'], 
          colors['gui'])

# 添加箭头连接
def add_arrow(start, end, label=None):
    plt.annotate('', xy=end, xytext=start, arrowprops=arrow_props)
    if label:
        mid_x = (start[0] + end[0]) / 2
        mid_y = (start[1] + end[1]) / 2
        plt.text(mid_x, mid_y, label, ha='center', va='center', fontsize=10, 
                fontweight='bold', bbox=dict(facecolor='white', alpha=0.7))

# 连接各模块
add_arrow((3, 7), (5, 7), '音频数据')
add_arrow((7, 7), (9, 7), 'MFCC特征')
add_arrow((10, 6), (10, 3.5), '识别结果')
add_arrow((10, 3.5), (7, 2.5), '命令')
add_arrow((6, 3.5), (6, 6), '状态反馈')

# 保存图片
plt.axis('off')
plt.tight_layout()
plt.savefig('temp/images/system_architecture.png', dpi=300, bbox_inches='tight')
plt.close()

print("系统架构图已保存到 temp/images/system_architecture.png") 