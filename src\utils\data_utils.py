#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据处理工具模块
"""

import os
import numpy as np
import librosa
import json
import random
from sklearn.model_selection import train_test_split
from scipy import signal

def load_audio_file(file_path, sample_rate=16000):
    """
    加载音频文件

    参数:
        file_path (str): 音频文件路径
        sample_rate (int): 采样率

    返回:
        numpy.ndarray: 音频数据
    """
    audio, _ = librosa.load(file_path, sr=sample_rate)
    return audio

def extract_mfcc_features(audio_data, sample_rate=16000, n_mfcc=13, n_fft=2048, hop_length=512):
    """
    提取MFCC特征

    参数:
        audio_data (numpy.ndarray): 音频数据
        sample_rate (int): 采样率
        n_mfcc (int): MFCC系数数量
        n_fft (int): FFT窗口大小
        hop_length (int): 帧移

    返回:
        numpy.ndarray: MFCC特征
    """
    mfccs = librosa.feature.mfcc(
        y=audio_data,
        sr=sample_rate,
        n_mfcc=n_mfcc,
        n_fft=n_fft,
        hop_length=hop_length
    )

    # 标准化特征
    mfccs = normalize_features(mfccs)

    return mfccs

def normalize_features(features):
    """
    标准化特征

    参数:
        features (numpy.ndarray): 特征矩阵

    返回:
        numpy.ndarray: 标准化后的特征
    """
    normalized_features = np.zeros_like(features)
    for i in range(features.shape[0]):
        mean = np.mean(features[i, :])
        std = np.std(features[i, :])
        normalized_features[i, :] = (features[i, :] - mean) / (std + 1e-10)

    return normalized_features

def pad_or_truncate(array, target_length):
    """
    填充或截断数组到指定长度
    
    参数:
        array (numpy.ndarray): 输入数组
        target_length (int): 目标长度
        
    返回:
        numpy.ndarray: 处理后的数组
    """
    if len(array.shape) > 1:
        current_length = array.shape[1]
        if current_length > target_length:
            return array[:, :target_length]
        else:
            # 填充
            padding = np.zeros((array.shape[0], target_length - current_length))
            return np.hstack((array, padding))
    else:
        current_length = len(array)
        if current_length > target_length:
            return array[:target_length]
        else:
            # 填充
            padding = np.zeros(target_length - current_length)
            return np.hstack((array, padding))

def prepare_dataset(data_dir, labels_file, test_size=0.2, val_size=0.1, random_state=42):
    """
    准备数据集

    参数:
        data_dir (str): 数据目录
        labels_file (str): 标签文件路径
        test_size (float): 测试集比例
        val_size (float): 验证集比例
        random_state (int): 随机种子

    返回:
        tuple: (X_train, y_train, X_val, y_val, X_test, y_test, label_map)
    """
    # 加载标签
    with open(labels_file, 'r', encoding='utf-8') as f:
        labels_data = json.load(f)

    # 获取所有音频文件和对应的标签
    audio_files = []
    labels = []

    for item in labels_data:
        audio_path = os.path.join(data_dir, item['file'])
        if os.path.exists(audio_path):
            audio_files.append(audio_path)
            labels.append(item['label'])

    # 创建标签映射
    unique_labels = sorted(set(labels))
    label_to_index = {label: i for i, label in enumerate(unique_labels)}
    index_to_label = {i: label for i, label in enumerate(unique_labels)}

    # 转换标签为索引
    label_indices = [label_to_index[label] for label in labels]

    # 使用分层抽样，确保每个集合都包含所有类别
    np.random.seed(random_state)

    # 按类别分组
    label_to_files = {}
    for i, label in enumerate(labels):
        if label not in label_to_files:
            label_to_files[label] = []
        label_to_files[label].append(i)

    # 为每个集合分配样本，确保每个类别至少有一个样本
    train_indices = []
    val_indices = []
    test_indices = []

    for label, indices in label_to_files.items():
        # 打乱索引
        np.random.shuffle(indices)

        # 确保每个集合至少有一个样本
        test_count = max(1, int(len(indices) * test_size))
        val_count = max(1, int(len(indices) * val_size))

        # 分配样本
        test_indices.extend(indices[:test_count])
        val_indices.extend(indices[test_count:test_count + val_count])
        train_indices.extend(indices[test_count + val_count:])

    # 划分数据集
    train_files = [audio_files[i] for i in train_indices]
    train_labels = [label_indices[i] for i in train_indices]

    val_files = [audio_files[i] for i in val_indices]
    val_labels = [label_indices[i] for i in val_indices]

    test_files = [audio_files[i] for i in test_indices]
    test_labels = [label_indices[i] for i in test_indices]

    # 打印划分信息
    print(f"数据集大小: {len(audio_files)}")
    print(f"训练集: {len(train_files)} 样本")
    print(f"验证集: {len(val_files)} 样本")
    print(f"测试集: {len(test_files)} 样本")

    # 创建标签映射
    label_map = {
        'index_to_label': index_to_label,
        'label_to_index': label_to_index
    }

    return train_files, train_labels, val_files, val_labels, test_files, test_labels, label_map

def process_audio_files(file_list, labels, sample_rate=16000, n_mfcc=13, max_length=None):
    """
    处理音频文件列表，提取MFCC特征

    参数:
        file_list (list): 音频文件路径列表
        labels (list): 标签列表
        sample_rate (int): 采样率
        n_mfcc (int): MFCC系数数量
        max_length (int, optional): MFCC特征的最大长度，如果为None则使用最长的特征长度

    返回:
        tuple: (X, y)，其中X是MFCC特征，y是独热编码的标签
    """
    # 首先计算最大长度（如果未指定）
    if max_length is None:
        max_length = 0
        for file_path in file_list:
            audio = load_audio_file(file_path, sample_rate)
            mfcc = extract_mfcc_features(audio, sample_rate, n_mfcc)
            max_length = max(max_length, mfcc.shape[1])

    # 处理所有文件
    X = []
    for file_path in file_list:
        audio = load_audio_file(file_path, sample_rate)
        mfcc = extract_mfcc_features(audio, sample_rate, n_mfcc)
        padded_mfcc = pad_or_truncate(mfcc, max_length)
        X.append(padded_mfcc)

    # 转换为numpy数组
    X = np.array(X)

    # 转换标签为独热编码
    num_classes = max(labels) + 1
    y = np.zeros((len(labels), num_classes))
    for i, label in enumerate(labels):
        y[i, label] = 1

    return X, y

def apply_spec_augment(features, time_mask_param=15, freq_mask_param=10, num_masks=2):
    """
    实施SpecAugment数据增强技术
    
    参数:
        features: 输入特征 (频率, 时间)
        time_mask_param: 时间掩码的最大长度
        freq_mask_param: 频率掩码的最大宽度
        num_masks: 掩码的数量
    
    返回:
        增强后的特征
    """
    # 创建特征副本
    augmented_features = np.copy(features)
    
    # 应用频率掩码
    for i in range(num_masks):
        f_mask_width = np.random.randint(1, freq_mask_param)
        f_start = np.random.randint(0, augmented_features.shape[0] - f_mask_width)
        augmented_features[f_start:f_start + f_mask_width, :] = 0
    
    # 应用时间掩码
    for i in range(num_masks):
        t_mask_width = np.random.randint(1, time_mask_param)
        t_start = np.random.randint(0, augmented_features.shape[1] - t_mask_width)
        augmented_features[:, t_start:t_start + t_mask_width] = 0
    
    return augmented_features

def augment_audio(audio_data, sample_rate=16000, noise_factor=0.005, shift_max=0.1, speed_factor=0.2, apply_all=False):
    """
    对音频数据进行增强
    
    参数:
        audio_data: 音频数据
        sample_rate: 采样率
        noise_factor: 噪声因子
        shift_max: 最大时移比例
        speed_factor: 速度变化因子
        apply_all: 是否应用所有增强
    
    返回:
        增强后的音频数据
    """
    
    # 创建音频副本
    augmented_audio = np.copy(audio_data)
    
    # 如果apply_all为True，则应用所有增强
    # 否则随机选择一种增强
    augmentation_choices = []
    if apply_all or np.random.rand() < 0.5:
        augmentation_choices.append('noise')
    if apply_all or np.random.rand() < 0.5:
        augmentation_choices.append('shift')
    if apply_all or np.random.rand() < 0.5:
        augmentation_choices.append('speed')
    if apply_all or np.random.rand() < 0.5:
        augmentation_choices.append('pitch')
    if apply_all or np.random.rand() < 0.5:
        augmentation_choices.append('volume')
    
    # 添加噪声
    if 'noise' in augmentation_choices:
        noise = np.random.randn(len(augmented_audio)) * noise_factor
        augmented_audio = augmented_audio + noise
    
    # 时移
    if 'shift' in augmentation_choices:
        shift = int(np.random.uniform(-shift_max, shift_max) * len(augmented_audio))
        if shift > 0:
            augmented_audio = np.pad(augmented_audio, (shift, 0), mode='constant')[:-shift]
        else:
            augmented_audio = np.pad(augmented_audio, (0, -shift), mode='constant')[-shift:]
    
    # 改变速度/音高
    if 'speed' in augmentation_choices:
        speed_change = np.random.uniform(1 - speed_factor, 1 + speed_factor)
        old_length = len(augmented_audio)
        new_length = int(old_length / speed_change)
        augmented_audio = signal.resample(augmented_audio, new_length)
        
        # 如果新长度小于原长度，填充至原长度
        if new_length < old_length:
            augmented_audio = np.pad(augmented_audio, (0, old_length - new_length), mode='constant')
        # 如果新长度大于原长度，截断至原长度
        elif new_length > old_length:
            augmented_audio = augmented_audio[:old_length]
    
    # 音高变化
    if 'pitch' in augmentation_choices:
        bins_per_octave = 12
        pitch_change = np.random.uniform(-2, 2)  # 上下两个半音
        augmented_audio = librosa.effects.pitch_shift(
            augmented_audio.astype(np.float32),
            sr=sample_rate, 
            n_steps=pitch_change, 
            bins_per_octave=bins_per_octave
        )
    
    # 音量变化
    if 'volume' in augmentation_choices:
        volume_change = np.random.uniform(0.8, 1.2)
        augmented_audio = augmented_audio * volume_change
        
    # 确保音频在[-1, 1]范围内
    augmented_audio = np.clip(augmented_audio, -1, 1)
    
    return augmented_audio

def save_json(data, file_path):
    """
    保存数据到JSON文件
    
    参数:
        data: 要保存的数据
        file_path (str): 文件路径
        
    返回:
        bool: 是否成功
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 保存为JSON
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        return True
    except Exception as e:
        print(f"保存JSON数据失败: {e}")
        return False

def load_json(file_path):
    """
    从JSON文件加载数据
    
    参数:
        file_path (str): 文件路径
        
    返回:
        dict: 加载的数据，加载失败则返回None
    """
    try:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return data
    except Exception as e:
        print(f"加载JSON数据失败: {e}")
        return None
