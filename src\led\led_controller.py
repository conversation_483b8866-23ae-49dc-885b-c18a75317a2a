#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LED控制模块
"""

import time

class LEDController:
    """LED控制类，模拟智能家居设备控制"""
    
    def __init__(self):
        """初始化LED控制器"""
        # 设备状态
        self.devices = {
            "light": False,
            "tv": False,
            "ac": False
        }
        
        # 命令映射
        self.command_map = {
            "开灯": ("light", True),
            "关灯": ("light", False),
            "打开电视": ("tv", True),
            "关闭电视": ("tv", False), 
            "打开空调": ("ac", True),
            "关闭空调": ("ac", False)
        }
        
        # 设备名称映射
        self.device_names = {
            "light": "灯光",
            "tv": "电视",
            "ac": "空调"
        }
    
    def execute_command(self, command):
        """
        执行语音命令
        
        参数:
            command (str): 语音命令
            
        返回:
            bool: 是否成功执行命令
        """
        if command not in self.command_map:
            print(f"未知命令: {command}")
            return False
        
        device, state = self.command_map[command]
        return self.control_device(device, state)
    
    def control_device(self, device, state):
        """
        控制设备状态
        
        参数:
            device (str): 设备名称
            state (bool): 设备状态
            
        返回:
            bool: 是否成功控制设备
        """
        if device not in self.devices:
            print(f"未知设备: {device}")
            return False
        
        # 更新设备状态
        self.devices[device] = state
        
        # 打印设备状态
        device_name = self.device_names.get(device, device)
        state_text = "开启" if state else "关闭"
        print(f"{device_name}已{state_text}")
        
        return True
    
    def get_device_status(self, device=None):
        """
        获取设备状态
        
        参数:
            device (str, optional): 设备名称，如果为None，则返回所有设备状态
            
        返回:
            dict or bool: 设备状态
        """
        if device is None:
            return self.devices
        
        if device not in self.devices:
            print(f"未知设备: {device}")
            return None
        
        return self.devices[device]
