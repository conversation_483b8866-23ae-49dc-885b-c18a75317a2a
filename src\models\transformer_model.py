#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于Transformer的哈萨克语语音命令识别模型
"""

import os
import numpy as np
# 使用tensorflow.keras
import tensorflow as tf
from tensorflow.keras import layers, models
from tensorflow.keras import optimizers
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau

class TransformerBlock(layers.Layer):
    """Transformer模块，包含多头注意力和前馈网络"""
    
    def __init__(self, embed_dim, num_heads, ff_dim, rate=0.1):
        super(TransformerBlock, self).__init__()
        self.att = layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim)
        self.ffn = tf.keras.Sequential([
            layers.Dense(ff_dim, activation="gelu"),  # 使用GELU激活函数替代ReLU
            layers.Dropout(rate),
            layers.Dense(embed_dim),
        ])
        self.layernorm1 = layers.LayerNormalization(epsilon=1e-6)
        self.layernorm2 = layers.LayerNormalization(epsilon=1e-6)
        self.dropout1 = layers.Dropout(rate)
        self.dropout2 = layers.Dropout(rate)
        
        # 添加注意力权重输出
        self.supports_masking = True
        
    def call(self, inputs, training=False):
        # 自注意力层和残差连接
        attn_output, attention_weights = self.att(
            inputs, inputs, return_attention_scores=True
        )
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(inputs + attn_output)
        
        # 前馈网络和第二个残差连接
        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        return self.layernorm2(out1 + ffn_output), attention_weights

    def get_config(self):
        config = super().get_config()
        return config

class PositionalEncoding(layers.Layer):
    """位置编码层"""
    
    def __init__(self, max_length, embedding_dim):
        super(PositionalEncoding, self).__init__()
        self.max_length = max_length
        self.embedding_dim = embedding_dim
        self.pos_encoding = self.positional_encoding(max_length, embedding_dim)
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "max_length": self.max_length,
            "embedding_dim": self.embedding_dim
        })
        return config
    
    def get_angles(self, pos, i, d_model):
        angle_rates = 1 / np.power(10000, (2 * (i//2)) / np.float32(d_model))
        return pos * angle_rates
    
    def positional_encoding(self, position, d_model):
        angle_rads = self.get_angles(
            np.arange(position)[:, np.newaxis],
            np.arange(d_model)[np.newaxis, :],
            d_model
        )
        
        # 对偶数位置应用sin
        angle_rads[:, 0::2] = np.sin(angle_rads[:, 0::2])
        
        # 对奇数位置应用cos
        angle_rads[:, 1::2] = np.cos(angle_rads[:, 1::2])
            
        pos_encoding = angle_rads[np.newaxis, ...]
            
        return tf.keras.backend.cast(pos_encoding, dtype=tf.keras.backend.floatx())
    
    def call(self, inputs):
        seq_length = tf.keras.backend.shape(inputs)[1]
        return inputs + self.pos_encoding[:, :seq_length, :]

class ConvEmbedding(layers.Layer):
    """卷积嵌入层，用于捕获局部模式"""
    
    def __init__(self, embed_dim):
        super(ConvEmbedding, self).__init__()
        self.conv1 = layers.Conv1D(filters=embed_dim, kernel_size=3, padding='same', activation='relu')
        self.conv2 = layers.Conv1D(filters=embed_dim, kernel_size=5, padding='same', activation='relu')
        self.layernorm = layers.LayerNormalization(epsilon=1e-6)
        
    def get_config(self):
        config = super().get_config()
        return config
        
    def call(self, inputs):
        conv1_output = self.conv1(inputs)
        conv2_output = self.conv2(inputs)
        # 结合两种不同大小的卷积核的输出
        combined = (conv1_output + conv2_output) / 2
        return self.layernorm(combined)

class KazakhSpeechTransformer(models.Model):
    """基于Transformer的哈萨克语语音命令识别模型"""
    
    def __init__(self, input_shape, num_classes, embed_dim=128, num_heads=8,
                 ff_dim=256, num_transformer_blocks=6, mlp_units=[256, 128], dropout=0.2):
        super(KazakhSpeechTransformer, self).__init__()
        
        # 输入层
        self.input_shape = input_shape
        self.num_classes = num_classes
        
        # 卷积嵌入层 - 改进版本，使用多层卷积
        self.conv_embedding = tf.keras.Sequential([
            layers.Conv1D(filters=64, kernel_size=3, padding='same', activation='relu'),
            layers.BatchNormalization(),
            layers.Conv1D(filters=128, kernel_size=3, padding='same', activation='relu'),
            layers.BatchNormalization(),
            layers.Conv1D(filters=embed_dim, kernel_size=3, padding='same', activation='relu'),
            layers.BatchNormalization(),
        ])
        
        # 嵌入层
        self.embedding = layers.Dense(embed_dim, activation=None)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(input_shape[0], embed_dim)
        
        # 添加注意力权重存储
        self.attention_weights = []
        
        # Dropout层
        self.dropout = layers.Dropout(dropout)
        
        # Transformer块
        self.transformer_blocks = []
        for i in range(num_transformer_blocks):
            # 随着深度增加，逐步减少dropout，防止过度正则化
            block_dropout = dropout * (1.0 - i * 0.05)
            block_dropout = max(0.1, block_dropout)  # 确保dropout不低于0.1
            
            self.transformer_blocks.append(
                TransformerBlock(embed_dim, num_heads, ff_dim, block_dropout)
            )
        
        # 全局池化 - 使用注意力池化而不是简单的平均池化
        self.global_avg_pooling = layers.GlobalAveragePooling1D()
        
        # 添加残差连接 - 跳过连接
        self.residual_layer = layers.Dense(embed_dim)
        
        # 添加层归一化
        self.layer_norm = layers.LayerNormalization(epsilon=1e-6)
        
        # MLP头部
        self.mlp_layers = []
        for dim in mlp_units:
            self.mlp_layers.extend([
                layers.Dense(dim, activation="relu"), 
                layers.BatchNormalization(),
                layers.Dropout(dropout)
            ])
        
        # 输出层
        self.output_layer = layers.Dense(num_classes, activation="softmax")
        
        # 构建模型
        inputs = layers.Input(shape=input_shape)
        self(inputs)
        
    def call(self, inputs, training=False):
        # 卷积嵌入
        x = self.conv_embedding(inputs)
        
        # 嵌入
        direct_embed = self.embedding(x)
        
        # 位置编码
        x = self.pos_encoding(direct_embed)
        
        # 应用dropout
        x = self.dropout(x, training=training)
        
        # 清空注意力权重列表
        self.attention_weights = []
        
        # Transformer块
        for transformer_block in self.transformer_blocks:
            x, attention_weights = transformer_block(x, training=training)
            self.attention_weights.append(attention_weights)
        
        # 残差连接 - 将位置编码前的嵌入与Transformer输出相加
        residual = self.residual_layer(tf.keras.backend.mean(direct_embed, axis=1))
        
        # 全局池化
        pooled = self.global_avg_pooling(x)
        
        # 应用残差连接
        combined = pooled + residual
        
        # 层归一化
        normalized = self.layer_norm(combined)
        
        # MLP头部
        x = normalized
        for layer in self.mlp_layers:
            x = layer(x, training=training)
        
        # 输出
        return self.output_layer(x)
        
    def model(self):
        """返回完整模型"""
        x = layers.Input(shape=self.input_shape)
        return tf.keras.Model(inputs=[x], outputs=self.call(x))

    def get_attention_weights(self):
        """获取注意力权重，用于可视化"""
        return self.attention_weights

    # 下面的方法不再使用，因为继承自keras.Model已经提供了这些方法
    # 删除这些方法，使用父类的方法
    """
    def train(self, X_train, y_train, X_val, y_val, batch_size=32, epochs=100, save_path=None):
        # 此方法不再需要，使用keras.Model.fit方法
        pass
    
    def predict(self, X):
        # 此方法不再需要，使用keras.Model.predict方法
        pass
    
    def evaluate(self, X_test, y_test):
        # 此方法不再需要，使用keras.Model.evaluate方法
        pass
    
    def save_model(self, save_path):
        # 此方法不再需要，使用keras.Model.save方法
        pass
    
    def load_model(self, model_path):
        # 此方法不再需要，使用keras.Model.load_weights方法
        pass
    """ 