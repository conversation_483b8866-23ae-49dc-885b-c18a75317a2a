#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
音频录制器模块
"""

import os
import wave
import time
import numpy as np
import sounddevice as sd
import soundfile as sf
import threading

class AudioRecorder:
    """音频录制类"""
    
    def __init__(self, sample_rate=16000, channels=1, temp_dir="temp"):
        """初始化录音器"""
        self.sample_rate = sample_rate
        self.channels = channels
        self.temp_dir = temp_dir
        self.temp_file = os.path.join(temp_dir, "temp_recording.wav")
        
        # 确保临时目录存在
        os.makedirs(temp_dir, exist_ok=True)
        
        # 增加变量用于连续录音
        self.is_recording = False
        self.recording_thread = None
        self.audio_data = None
        self.recording_duration = 3  # 默认录音时长
    
    def record(self, duration=3):
        """
        录制音频
        
        参数:
            duration (float): 录音时长（秒）
            
        返回:
            numpy.ndarray: 录制的音频数据
        """
        try:
            print(f"开始录音，时长 {duration} 秒...")
            recording = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=self.channels
            )
            sd.wait()  # 等待录音完成
            print("录音完成")
            
            # 保存到临时文件
            sf.write(self.temp_file, recording, self.sample_rate)
            
            return recording.flatten() if self.channels == 1 else recording
        except Exception as e:
            print(f"录音失败: {e}")
            return None
    
    def start_recording(self):
        """启动录音过程"""
        if self.is_recording:
            print("已经在录音")
            return False
        
        # 设置录音状态
        self.is_recording = True
        self.audio_data = None
        
        # 启动录音线程
        self.recording_thread = threading.Thread(target=self._record_thread)
        self.recording_thread.daemon = True
        self.recording_thread.start()
        
        print("录音已启动")
        return True
    
    def _record_thread(self):
        """录音线程函数"""
        try:
            # 使用sounddevice进行录音
            self.audio_data = self.record(self.recording_duration)
        except Exception as e:
            print(f"录音线程异常: {e}")
        finally:
            # 无论发生什么，确保录音状态被重置
            self.is_recording = False
    
    def stop_recording(self):
        """停止录音过程"""
        if not self.is_recording:
            print("未在录音")
            return False
        
        # 仅标记停止，让线程自行结束
        self.is_recording = False
        
        # 等待线程结束
        if self.recording_thread and self.recording_thread.is_alive():
            self.recording_thread.join(timeout=1.0)
        
        print("录音已停止")
        return True
    
    def get_audio_data(self):
        """获取录音数据"""
        return self.audio_data
    
    def save_recording(self, audio_data, file_path):
        """
        保存录音到指定文件
        
        参数:
            audio_data (numpy.ndarray): 录音数据
            file_path (str): 保存路径
        """
        try:
            sf.write(file_path, audio_data, self.sample_rate)
            print(f"录音已保存到: {file_path}")
            return True
        except Exception as e:
            print(f"保存录音失败: {e}")
            return False
    
    def get_last_recording_path(self):
        """获取最后一次录音的临时文件路径"""
        return self.temp_file if os.path.exists(self.temp_file) else None
