# 哈萨克语语音识别系统

这是一个基于Python的哈萨克语语音识别系统，能够通过麦克风采集语音数据，调用模型进行哈萨克语识别，并根据识别结果控制设备（灯、电视、空调）。

## 功能特点

- 实时语音采集与处理
- MFCC特征提取（包括delta和delta-delta特征）
- 深度学习模型训练与优化（使用交叉验证提高准确率）
- 语音命令识别（支持5种哈萨克语命令）
- 设备控制（灯、电视、空调）
- 用户友好的图形界面
- 音频波形和频谱图可视化
- 支持上传音频文件进行识别

## 系统架构

系统由以下几个主要模块组成：

1. **音频处理模块**：负责从麦克风采集语音数据或加载音频文件
2. **特征提取模块**：将语音转换为MFCC特征（包括delta和delta-delta特征）
3. **模型训练模块**：使用交叉验证训练深度学习模型识别哈萨克语命令
4. **设备控制模块**：根据识别结果控制设备（灯、电视、空调）
5. **图形用户界面**：提供用户友好的操作界面，包括音频可视化和设备状态显示

## 安装与配置

### 环境要求

- Python 3.8+
- 虚拟环境（推荐）

### 安装步骤

1. 克隆或下载本项目到本地

2. 创建并激活虚拟环境

```bash
python -m venv venv
.\venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac
```

3. 安装依赖包

```bash
pip install numpy matplotlib librosa tensorflow sounddevice soundfile scikit-learn pillow
```

## 使用方法

### 启动图形界面

```bash
python gui.py
```

### 训练模型

```bash
python train_improved_v2.py --data_dir data/raw --epochs 50 --batch_size 4 --max_length 50 --n_folds 5
```

参数说明：
- `--data_dir`：数据目录
- `--epochs`：训练轮数
- `--batch_size`：批量大小
- `--max_length`：MFCC特征的最大长度
- `--n_folds`：交叉验证折数

### 命令行预测

使用训练好的模型进行预测：

```bash
python predict.py --audio_path path/to/audio.wav
```

如果不提供 `--audio_path` 参数，将使用麦克风录音：

```bash
python predict.py
```

参数说明：
- `--audio_path`：音频文件路径（可选）
- `--model_path`：模型路径（默认为 `models/kazakh_speech_model_final.h5`）
- `--threshold`：预测置信度阈值（默认为 0.5）

## 目录结构

```
哈萨克语语音识别系统/
├── data/                  # 数据目录
│   ├── raw/               # 原始音频数据
│   └── processed/         # 处理后的数据
├── models/                # 模型目录
├── src/                   # 源代码
│   ├── audio/             # 音频处理模块
│   ├── features/          # 特征提取模块
│   ├── gui/               # 图形界面模块
│   ├── led/               # LED控制模块
│   └── utils/             # 工具函数
├── main.py                # 主程序
├── train_model.py         # 模型训练脚本
├── test_recognition.py    # 测试脚本
└── README.md              # 说明文档
```

## 支持的语音命令

系统支持以下哈萨克语语音命令：

- 打开红灯
- 关闭红灯
- 打开绿灯
- 关闭绿灯
- 打开蓝灯
- 关闭蓝灯
- 打开黄灯
- 关闭黄灯
- 全部打开
- 全部关闭
- 红灯闪烁
- 绿灯闪烁
- 蓝灯闪烁
- 黄灯闪烁

## 开发流程

1. **数据收集与标注**：收集哈萨克语语音数据并进行标注
2. **特征提取**：从语音数据中提取MFCC特征
3. **模型训练**：训练深度学习模型识别语音命令
4. **模型优化**：调整模型参数以提高识别准确率
5. **系统集成**：将各模块集成为完整系统
6. **测试与评估**：测试系统性能并进行评估

## 注意事项

- 确保麦克风正常工作
- 在安静的环境中使用效果更佳
- 首次使用需要训练模型
- LED控制功能需要根据实际硬件进行配置
