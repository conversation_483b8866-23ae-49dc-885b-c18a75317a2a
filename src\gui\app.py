#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
哈萨克语语音识别系统GUI界面
"""

import os
import sys
import time
import json
import threading
import tkinter as tk
import tkinter.font
from tkinter import ttk, filedialog, messagebox
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
import matplotlib.font_manager as fm

# 解决matplotlib中文显示问题
import os
import matplotlib.font_manager as fm

# 创建字体目录（如果不存在）
font_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'fonts')
os.makedirs(font_dir, exist_ok=True)

# 检查字体文件是否存在，如果不存在则创建一个简单的提示文件
font_info_file = os.path.join(font_dir, 'README.txt')
if not os.path.exists(font_info_file):
    with open(font_info_file, 'w', encoding='utf-8') as f:
        f.write("""
请在此目录放置中文字体文件（.ttf或.otf格式）
推荐字体：
1. SimSun.ttf (宋体)
2. SimHei.ttf (黑体)
3. Microsoft YaHei.ttf (微软雅黑)

字体文件通常可以在Windows系统的C:\\Windows\\Fonts目录中找到
将字体文件复制到此目录后重启应用程序
""")

# 查找字体目录中的所有字体文件
font_files = []
for file in os.listdir(font_dir):
    if file.lower().endswith(('.ttf', '.otf')):
        font_files.append(os.path.join(font_dir, file))

# 如果找到字体文件，使用第一个
if font_files:
    # 添加字体文件到matplotlib字体管理器
    for font_file in font_files:
        fm.fontManager.addfont(font_file)
        print(f"已加载字体文件: {font_file}")

    # 获取字体属性
    font_prop = fm.FontProperties(fname=font_files[0])
    font_name = font_prop.get_name()

    # 设置matplotlib字体
    plt.rcParams['font.sans-serif'] = [font_name, 'sans-serif']
    print(f"使用字体: {font_name}")
else:
    # 尝试使用系统字体
    print("未找到字体文件，尝试使用系统字体...")
    plt.rcParams['font.sans-serif'] = ['SimSun', 'Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']

# 解决负号显示问题
plt.rcParams['axes.unicode_minus'] = False
matplotlib.use("TkAgg")
import random

# 导入自定义模块
from src.audio.recorder import AudioRecorder
from src.features.mfcc_extractor import MFCCExtractor
from src.led.led_controller import LEDController

class KazakhSpeechRecognitionApp:
    """哈萨克语语音识别系统GUI应用类"""

    def __init__(self):
        """初始化应用"""
        self.root = tk.Tk()
        self.root.title("哈萨克语语音识别系统")
        self.root.geometry("900x700")  # 增加窗口大小
        self.root.minsize(900, 700)    # 增加最小窗口大小

        # 设置字体以解决中文显示问题
        # 尝试使用字体文件
        if font_files:
            # 使用第一个找到的字体文件
            self.available_font = font_name
        else:
            # 尝试多种常见的中文字体
            chinese_fonts = ["SimSun", "Microsoft YaHei", "SimHei", "NSimSun", "FangSong", "KaiTi"]

            # 找到系统上可用的中文字体
            self.available_font = None
            for font in chinese_fonts:
                try:
                    tk.font.Font(family=font, size=10)  # 测试字体是否可用
                    self.available_font = font
                    break
                except:
                    continue

            # 如果找不到中文字体，使用系统默认字体
            if not self.available_font:
                self.available_font = "TkDefaultFont"

        print(f"GUI使用字体: {self.available_font}")

        # 设置默认字体
        self.default_font = tk.font.nametofont("TkDefaultFont")
        self.default_font.configure(family=self.available_font, size=10)
        self.root.option_add("*Font", self.default_font)

        # 创建自定义样式
        self.style = ttk.Style()
        self.style.configure("TLabelframe.Label", font=(self.available_font, 12, "bold"))
        self.style.configure("TLabel", font=(self.available_font, 10))
        self.style.configure("TButton", font=(self.available_font, 10))

        # 初始化组件
        self.recorder = AudioRecorder()
        self.mfcc_extractor = MFCCExtractor()
        self.led_controller = LEDController()

        # 初始化音频处理器
        from src.audio.audio_processor import AudioProcessor
        self.audio_processor = AudioProcessor()

        # 创建界面
        self._create_widgets()

        # 初始化变量
        self.is_recording = False
        self.recording_thread = None
        self.model = None
        # 使用数据集中的5种命令
        self.commands = ["打开灯", "关闭灯", "打开电视", "关闭电视", "打开空调"]

        # 模型输出到界面显示的映射 - 使用正确的映射
        # 由于我们已经修复了标签映射，这里不需要额外的映射
        self.model_to_display = {
            "打开灯": "打开灯",         # 0001-kd
            "关闭灯": "关闭灯",         # 0002-gd
            "打开电视": "打开电视",     # 0003-kds
            "关闭电视": "关闭电视",     # 0004-gds
            "打开空调": "打开空调"      # 0005-kkt
        }

        # 界面显示到模型输出的映射（反向映射）
        self.display_to_model = {v: k for k, v in self.model_to_display.items()}

    def _create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题标签
        title_label = ttk.Label(main_frame, text="哈萨克语语音识别系统", font=(self.available_font, 16, "bold"))
        title_label.pack(pady=10)

        # 创建选项卡控件
        tab_control = ttk.Notebook(main_frame)

        # 创建各个选项卡
        recognition_tab = ttk.Frame(tab_control)
        training_tab = ttk.Frame(tab_control)
        settings_tab = ttk.Frame(tab_control)

        tab_control.add(recognition_tab, text="语音识别")
        tab_control.add(training_tab, text="模型训练")
        tab_control.add(settings_tab, text="设置")

        tab_control.pack(expand=True, fill=tk.BOTH)

        # 设置各个选项卡的内容
        self._setup_recognition_tab(recognition_tab)
        self._setup_training_tab(training_tab)
        self._setup_settings_tab(settings_tab)

        # 创建状态栏
        status_frame = ttk.Frame(self.root, relief=tk.SUNKEN, padding=(5, 2))
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)

        # 创建版本标签
        version_label = ttk.Label(status_frame, text="v1.0.0")
        version_label.pack(side=tk.RIGHT)

    def _setup_recognition_tab(self, parent):
        """设置语音识别选项卡"""
        # 创建主框架
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建三个区域的框架
        # 左侧区域 - 控制和结果
        left_area = ttk.Frame(main_frame, padding=10, width=300)
        left_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 中间区域 - 设备图标
        middle_area = ttk.Frame(main_frame, padding=10, width=300)
        middle_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 右侧区域 - 波形和MFCC
        right_area = ttk.Frame(main_frame, padding=10, width=300)
        right_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 左侧区域内的上下分栏
        left_top = ttk.Frame(left_area, padding=5)
        left_top.pack(fill=tk.BOTH, expand=True)

        left_bottom = ttk.Frame(left_area, padding=5)
        left_bottom.pack(fill=tk.X, pady=5)

        # 右侧区域内的上下分栏
        right_top = ttk.Frame(right_area, padding=5)
        right_top.pack(fill=tk.BOTH, expand=True)

        right_bottom = ttk.Frame(right_area, padding=5)
        right_bottom.pack(fill=tk.BOTH, expand=True)

        # 左侧区域 - 上部：录音控制
        record_frame = ttk.LabelFrame(left_top, text="录音控制", padding=10)
        record_frame.pack(fill=tk.X, pady=5)

        self.record_button = ttk.Button(record_frame, text="开始录音", command=self._toggle_recording)
        self.record_button.pack(fill=tk.X, pady=5)

        self.save_button = ttk.Button(record_frame, text="保存录音", command=self._save_recording, state=tk.DISABLED)
        self.save_button.pack(fill=tk.X, pady=5)

        # 添加上传音频文件按钮
        self.upload_button = ttk.Button(record_frame, text="上传音频文件", command=self._upload_audio_file)
        self.upload_button.pack(fill=tk.X, pady=5)

        # 左侧区域 - 下部：识别结果
        result_frame = ttk.LabelFrame(left_bottom, text="识别结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.result_text = tk.Text(result_frame, height=10, width=30, wrap=tk.WORD, state=tk.DISABLED)
        self.result_text.pack(fill=tk.BOTH, expand=True)

        # 初始化文本标签
        self._init_text_tags()

        # 右侧区域 - 上部：波形显示
        waveform_frame = ttk.LabelFrame(right_top, text="音频波形", padding=10)
        waveform_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建matplotlib图形
        self.waveform_figure = plt.Figure(figsize=(5, 3), dpi=100)
        self.waveform_canvas = FigureCanvasTkAgg(self.waveform_figure, waveform_frame)
        self.waveform_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 右侧区域 - 下部：MFCC特征显示
        mfcc_frame = ttk.LabelFrame(right_bottom, text="MFCC特征", padding=10)
        mfcc_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建matplotlib图形
        self.mfcc_figure = plt.Figure(figsize=(5, 3), dpi=100)
        self.mfcc_canvas = FigureCanvasTkAgg(self.mfcc_figure, mfcc_frame)
        self.mfcc_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 中间区域：设备状态图标显示
        self._setup_device_icons(middle_area)

    def _setup_training_tab(self, parent):
        """设置模型训练选项卡"""
        # 创建左右分栏
        left_frame = ttk.Frame(parent, padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        right_frame = ttk.Frame(parent, padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 左侧：数据集管理
        dataset_frame = ttk.LabelFrame(left_frame, text="数据集管理", padding=10)
        dataset_frame.pack(fill=tk.X, pady=5)

        ttk.Button(dataset_frame, text="选择数据集目录", command=self._select_dataset_dir).pack(fill=tk.X, pady=5)
        ttk.Button(dataset_frame, text="处理数据集", command=self._process_dataset).pack(fill=tk.X, pady=5)

        # 左侧：模型训练
        training_frame = ttk.LabelFrame(left_frame, text="模型训练", padding=10)
        training_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(training_frame, text="批量大小:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.batch_size_var = tk.StringVar(value="32")
        ttk.Entry(training_frame, textvariable=self.batch_size_var, width=10).grid(row=0, column=1, sticky=tk.W, pady=5)

        ttk.Label(training_frame, text="训练轮数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.epochs_var = tk.StringVar(value="50")
        ttk.Entry(training_frame, textvariable=self.epochs_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=5)

        ttk.Button(training_frame, text="开始训练", command=self._start_training).grid(row=2, column=0, columnspan=2, sticky=tk.EW, pady=10)

        # 右侧：训练日志
        log_frame = ttk.LabelFrame(right_frame, text="训练日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text = tk.Text(log_frame, height=20, width=40, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        log_scrollbar = ttk.Scrollbar(self.log_text, orient="vertical", command=self.log_text.yview)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

    def _setup_device_icons(self, parent):
        """设置中间的设备状态图标显示"""
        # 创建设备图标框架
        icon_frame = ttk.LabelFrame(parent, text="设备状态", padding=10)
        icon_frame.pack(fill=tk.BOTH, expand=True, pady=5, padx=5)

        # 创建标题标签
        title_label = ttk.Label(icon_frame, text="语音控制设备", font=(self.available_font, 18, "bold"))
        title_label.pack(pady=10)

        # 创建设备图标容器 - 垂直排列
        device_frame = ttk.Frame(icon_frame)
        device_frame.pack(fill=tk.BOTH, expand=True)

        # 设置图标大小
        icon_size = 100

        # 灯光图标
        light_frame = ttk.Frame(device_frame)
        light_frame.pack(pady=15)
        self.light_icon = tk.Canvas(light_frame, width=icon_size, height=icon_size, bg="white", highlightthickness=3, highlightbackground="black")
        self.light_icon.pack(pady=5)
        # 绘制灯泡图标
        self.light_icon.create_oval(15, 25, icon_size-15, icon_size-15, fill="gray", tags="light_icon")
        self.light_icon.create_rectangle(icon_size/2-7, 5, icon_size/2+7, 25, fill="gray", tags="light_icon")
        ttk.Label(light_frame, text="灯光", font=(self.available_font, 16, "bold")).pack()

        # 电视图标
        tv_frame = ttk.Frame(device_frame)
        tv_frame.pack(pady=15)
        self.tv_icon = tk.Canvas(tv_frame, width=icon_size, height=icon_size, bg="white", highlightthickness=3, highlightbackground="black")
        self.tv_icon.pack(pady=5)
        # 绘制电视图标
        self.tv_icon.create_rectangle(10, 15, icon_size-10, icon_size-25, fill="gray", tags="tv_icon")
        self.tv_icon.create_rectangle(icon_size/2-15, icon_size-25, icon_size/2+15, icon_size-5, fill="gray", tags="tv_icon")
        ttk.Label(tv_frame, text="电视", font=(self.available_font, 16, "bold")).pack()

        # 空调图标
        ac_frame = ttk.Frame(device_frame)
        ac_frame.pack(pady=15)
        self.ac_icon = tk.Canvas(ac_frame, width=icon_size, height=icon_size, bg="white", highlightthickness=3, highlightbackground="black")
        self.ac_icon.pack(pady=5)
        # 绘制空调图标
        self.ac_icon.create_rectangle(10, 20, icon_size-10, icon_size-20, fill="gray", tags="ac_icon")
        self.ac_icon.create_line(20, 40, icon_size-20, 40, fill="black", width=3)
        self.ac_icon.create_line(20, 60, icon_size-20, 60, fill="black", width=3)
        ttk.Label(ac_frame, text="空调", font=(self.available_font, 16, "bold")).pack()

    def _setup_device_control_panel(self, parent):
        """设置设备控制面板"""
        # 创建控制面板 - 使用大号字体和明显的边框
        control_frame = ttk.LabelFrame(parent, text="设备控制", padding=10)
        control_frame.pack(fill=tk.BOTH, expand=True, pady=5, padx=5)

        # 创建左右分栏 - 确保有足够的空间
        left_frame = ttk.Frame(control_frame, width=350, height=150)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        left_frame.pack_propagate(False)  # 防止框架被子组件压缩

        right_frame = ttk.Frame(control_frame, width=350, height=150)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        right_frame.pack_propagate(False)  # 防止框架被子组件压缩

        # 左侧：设备状态显示
        status_frame = ttk.LabelFrame(left_frame, text="设备状态", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 设置标签样式
        style = ttk.Style()
        style.configure("DeviceStatus.TLabel", font=("SimSun", 14, "bold"))

        # 创建设备状态指示器
        device_frame = ttk.Frame(status_frame)
        device_frame.pack(fill=tk.X, pady=5)

        # 灯光
        light_frame = ttk.Frame(device_frame)
        light_frame.pack(side=tk.LEFT, expand=True, padx=10)
        self.light_indicator = tk.Canvas(light_frame, width=60, height=60, bg="white", highlightthickness=2, highlightbackground="black")
        self.light_indicator.pack(pady=5)
        self.light_indicator.create_oval(5, 5, 55, 55, fill="gray", tags="light_indicator")
        ttk.Label(light_frame, text="灯光", font=(self.available_font, 14, "bold")).pack()

        # 电视
        tv_frame = ttk.Frame(device_frame)
        tv_frame.pack(side=tk.LEFT, expand=True, padx=10)
        self.tv_indicator = tk.Canvas(tv_frame, width=60, height=60, bg="white", highlightthickness=2, highlightbackground="black")
        self.tv_indicator.pack(pady=5)
        self.tv_indicator.create_oval(5, 5, 55, 55, fill="gray", tags="tv_indicator")
        ttk.Label(tv_frame, text="电视", font=(self.available_font, 14, "bold")).pack()

        # 空调
        ac_frame = ttk.Frame(device_frame)
        ac_frame.pack(side=tk.LEFT, expand=True, padx=10)
        self.ac_indicator = tk.Canvas(ac_frame, width=60, height=60, bg="white", highlightthickness=2, highlightbackground="black")
        self.ac_indicator.pack(pady=5)
        self.ac_indicator.create_oval(5, 5, 55, 55, fill="gray", tags="ac_indicator")
        ttk.Label(ac_frame, text="空调", font=(self.available_font, 14, "bold")).pack()

        # 右侧：设备控制按钮
        control_buttons_frame = ttk.LabelFrame(right_frame, text="控制按钮", padding=10)
        control_buttons_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 设置按钮框架样式
        style = ttk.Style()
        style.configure("Control.TFrame", background="#f0f0f0")

        # 设备控制按钮
        button_frame = ttk.Frame(control_buttons_frame)
        button_frame.pack(fill=tk.BOTH, expand=True)

        # 创建自定义样式
        style = ttk.Style()
        style.configure("Device.TButton", font=(self.available_font, 12, "bold"))

        # 灯光控制
        light_control_frame = ttk.Frame(button_frame)
        light_control_frame.pack(fill=tk.X, pady=5)
        ttk.Label(light_control_frame, text="灯光:", font=(self.available_font, 14, "bold")).pack(side=tk.LEFT, padx=5)
        ttk.Button(light_control_frame, text="打开", width=8, style="Device.TButton", command=lambda: self._control_device("light", "on")).pack(side=tk.LEFT, padx=5)
        ttk.Button(light_control_frame, text="关闭", width=8, style="Device.TButton", command=lambda: self._control_device("light", "off")).pack(side=tk.LEFT, padx=5)
        ttk.Button(light_control_frame, text="闪烁", width=8, style="Device.TButton", command=lambda: self._control_device("light", "blink")).pack(side=tk.LEFT, padx=5)

        # 电视控制
        tv_control_frame = ttk.Frame(button_frame)
        tv_control_frame.pack(fill=tk.X, pady=5)
        ttk.Label(tv_control_frame, text="电视:", font=(self.available_font, 14, "bold")).pack(side=tk.LEFT, padx=5)
        ttk.Button(tv_control_frame, text="打开", width=8, style="Device.TButton", command=lambda: self._control_device("tv", "on")).pack(side=tk.LEFT, padx=5)
        ttk.Button(tv_control_frame, text="关闭", width=8, style="Device.TButton", command=lambda: self._control_device("tv", "off")).pack(side=tk.LEFT, padx=5)

        # 空调控制
        ac_control_frame = ttk.Frame(button_frame)
        ac_control_frame.pack(fill=tk.X, pady=5)
        ttk.Label(ac_control_frame, text="空调:", font=(self.available_font, 14, "bold")).pack(side=tk.LEFT, padx=5)
        ttk.Button(ac_control_frame, text="打开", width=8, style="Device.TButton", command=lambda: self._control_device("ac", "on")).pack(side=tk.LEFT, padx=5)
        ttk.Button(ac_control_frame, text="关闭", width=8, style="Device.TButton", command=lambda: self._control_device("ac", "off")).pack(side=tk.LEFT, padx=5)

        # 全局控制
        global_control_frame = ttk.Frame(button_frame)
        global_control_frame.pack(fill=tk.X, pady=10)
        ttk.Button(global_control_frame, text="全部打开", width=15, style="Device.TButton", command=self._all_devices_on).pack(side=tk.LEFT, padx=10, expand=True)
        ttk.Button(global_control_frame, text="全部关闭", width=15, style="Device.TButton", command=self._all_devices_off).pack(side=tk.LEFT, padx=10, expand=True)

    def _setup_settings_tab(self, parent):
        """设置设置选项卡"""
        settings_frame = ttk.Frame(parent, padding=10)
        settings_frame.pack(fill=tk.BOTH, expand=True)

        # 音频设置
        audio_frame = ttk.LabelFrame(settings_frame, text="音频设置", padding=10)
        audio_frame.pack(fill=tk.X, pady=5)

        ttk.Label(audio_frame, text="采样率:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.sample_rate_var = tk.StringVar(value="16000")
        ttk.Entry(audio_frame, textvariable=self.sample_rate_var, width=10).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Label(audio_frame, text="Hz").grid(row=0, column=2, sticky=tk.W, pady=5)

        ttk.Label(audio_frame, text="帧大小:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.chunk_var = tk.StringVar(value="1024")
        ttk.Entry(audio_frame, textvariable=self.chunk_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=5)

        # MFCC设置
        mfcc_frame = ttk.LabelFrame(settings_frame, text="MFCC设置", padding=10)
        mfcc_frame.pack(fill=tk.X, pady=5)

        ttk.Label(mfcc_frame, text="MFCC系数:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.n_mfcc_var = tk.StringVar(value="13")
        ttk.Entry(mfcc_frame, textvariable=self.n_mfcc_var, width=10).grid(row=0, column=1, sticky=tk.W, pady=5)

        ttk.Label(mfcc_frame, text="FFT窗口大小:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.n_fft_var = tk.StringVar(value="2048")
        ttk.Entry(mfcc_frame, textvariable=self.n_fft_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=5)

        ttk.Label(mfcc_frame, text="帧移:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.hop_length_var = tk.StringVar(value="512")
        ttk.Entry(mfcc_frame, textvariable=self.hop_length_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=5)

        # 模型设置
        model_frame = ttk.LabelFrame(settings_frame, text="模型设置", padding=10)
        model_frame.pack(fill=tk.X, pady=5)

        ttk.Label(model_frame, text="模型路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.model_path_var = tk.StringVar(value="models/kazakh_speech_model.h5")
        ttk.Entry(model_frame, textvariable=self.model_path_var, width=30).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Button(model_frame, text="浏览", command=self._browse_model_path).grid(row=0, column=2, sticky=tk.W, pady=5, padx=5)

        # 保存设置按钮
        ttk.Button(settings_frame, text="保存设置", command=self._save_settings).pack(pady=10)

    def _toggle_recording(self):
        """切换录音状态"""
        if not self.is_recording:
            # 开始录音
            self.recorder.start_recording()
            self.is_recording = True
            self.record_button.config(text="停止录音")
            self.save_button.config(state=tk.DISABLED)
            self.status_label.config(text="正在录音...")

            # 启动波形更新线程
            self.recording_thread = threading.Thread(target=self._update_waveform)
            self.recording_thread.daemon = True
            self.recording_thread.start()
        else:
            # 停止录音
            self.recorder.stop_recording()
            self.is_recording = False
            self.record_button.config(text="开始录音")
            self.save_button.config(state=tk.NORMAL)
            self.status_label.config(text="录音已停止")

            # 获取录音数据并显示波形
            audio_data = self.recorder.get_audio_data()
            if audio_data is not None:
                self._display_waveform(audio_data)
                self._extract_and_display_mfcc(audio_data)
                self._recognize_speech(audio_data)

    def _update_waveform(self):
        """更新波形显示"""
        while self.is_recording:
            audio_data = self.recorder.get_audio_data()
            if audio_data is not None:
                self._display_waveform(audio_data)
            time.sleep(0.1)

    def _display_waveform(self, audio_data):
        """显示音频波形"""
        self.waveform_figure.clear()
        ax = self.waveform_figure.add_subplot(111)
        ax.plot(audio_data)

        # 设置中文标题和标签，确保使用正确的字体
        if font_files:
            # 如果有字体文件，直接使用
            font_props = matplotlib.font_manager.FontProperties(fname=font_files[0])
        else:
            # 否则使用系统字体
            font_props = matplotlib.font_manager.FontProperties(family=self.available_font)

        ax.set_title("音频波形", fontproperties=font_props)
        ax.set_xlabel("样本", fontproperties=font_props)
        ax.set_ylabel("振幅", fontproperties=font_props)

        self.waveform_figure.tight_layout()
        self.waveform_canvas.draw()

    def _extract_and_display_mfcc(self, audio_data):
        """提取并显示MFCC特征"""
        # 提取MFCC特征
        mfccs = self.mfcc_extractor.extract_from_audio(audio_data)

        # 显示MFCC特征
        self.mfcc_figure.clear()
        ax = self.mfcc_figure.add_subplot(111)
        cax = ax.imshow(mfccs, aspect='auto', origin='lower', interpolation='none')

        # 设置中文标题和标签，确保使用正确的字体
        if font_files:
            # 如果有字体文件，直接使用
            font_props = matplotlib.font_manager.FontProperties(fname=font_files[0])
        else:
            # 否则使用系统字体
            font_props = matplotlib.font_manager.FontProperties(family=self.available_font)

        ax.set_title("MFCC特征", fontproperties=font_props)
        ax.set_xlabel("帧", fontproperties=font_props)
        ax.set_ylabel("MFCC系数", fontproperties=font_props)

        self.mfcc_figure.colorbar(cax)
        self.mfcc_figure.tight_layout()
        self.mfcc_canvas.draw()

    def _recognize_speech(self, audio_data, file_path=None):
        """
        识别语音

        参数:
            audio_data: 音频数据
            file_path: 音频文件路径，如果有的话
        """
        # 加载标签映射
        try:
            label_map_path = os.path.join("models", "label_map.json")
            if os.path.exists(label_map_path):
                with open(label_map_path, 'r', encoding='utf-8') as f:
                    label_map = json.load(f)
                index_to_label = {int(k): v for k, v in label_map['index_to_label'].items()}
                print(f"已加载标签映射: {index_to_label}")
            else:
                print(f"标签映射文件不存在: {label_map_path}")
                index_to_label = {
                    0: "全部打开", 1: "关闭红灯", 2: "关闭绿灯", 3: "打开红灯", 4: "打开绿灯"
                }
        except Exception as e:
            print(f"加载标签映射失败: {e}")
            index_to_label = {
                0: "全部打开", 1: "关闭红灯", 2: "关闭绿灯", 3: "打开红灯", 4: "打开绿灯"
            }

        # 检查是否已加载模型
        if self.model is None:
            # 如果模型未加载，尝试加载模型
            model_path = self.model_path_var.get()
            try:
                # 导入必要的模块
                from src.models.kazakh_speech_model import KazakhSpeechModel
                from src.utils.data_utils import pad_or_truncate

                # 获取类别数量
                num_classes = len(index_to_label)

                # 设置输入形状 (MFCC系数, 帧数, 通道数)
                input_shape = (13, 9781, 1)  # 模型期望的输入形状

                # 加载模型
                self.model = KazakhSpeechModel(input_shape, num_classes, model_path=model_path)
                print(f"模型加载成功: {model_path}")
                self.status_label.config(text=f"模型加载成功: {model_path}")
            except Exception as e:
                # 如果加载失败，使用演示模式
                print(f"模型加载失败: {e}")
                self.status_label.config(text="模型加载失败，使用演示模式")
                self.model = None

        # 提取MFCC特征
        mfccs = self.mfcc_extractor.extract_from_audio(audio_data)

        # 使用模型进行预测
        if self.model is not None:
            try:
                # 导入必要的模块
                from src.utils.data_utils import pad_or_truncate
                import numpy as np

                # 预处理特征以适应模型输入
                # 模型需要固定长度的输入，这里使用9781帧
                padded_mfccs = pad_or_truncate(mfccs, 9781)

                # 重塑特征以适应模型输入 - 确保形状为 (1, 13, 9781, 1)
                # mfccs的形状是 (13, frames)，需要转换为 (1, 13, 9781, 1)
                X = padded_mfccs.reshape(1, padded_mfccs.shape[0], padded_mfccs.shape[1], 1)

                print(f"MFCC特征形状: {mfccs.shape}")
                print(f"填充后MFCC特征形状: {padded_mfccs.shape}")
                print(f"模型输入形状: {X.shape}")

                # 使用模型预测
                predictions = self.model.predict(X)[0]
                predicted_index = np.argmax(predictions)
                model_result = index_to_label.get(predicted_index, "未知命令")
                confidence = predictions[predicted_index]

                # 将模型输出映射到界面显示
                recognized_text = self.model_to_display.get(model_result, model_result)

                print(f"模型识别结果: {model_result} -> 显示为: {recognized_text} (置信度: {confidence:.2f})")
                self.status_label.config(text=f"识别结果: {recognized_text} (置信度: {confidence:.2f})")
            except Exception as e:
                print(f"预测失败: {e}")
                self.status_label.config(text=f"预测失败: {e}")
                # 如果预测失败，使用演示模式
                recognized_text = random.choice(self.commands)
        else:
            # 如果模型未加载，使用演示模式
            if file_path:
                # 从文件名中提取命令（仅用于演示）
                filename = os.path.basename(file_path)
                if "0001-kd" in filename:
                    model_result = "打开灯"
                elif "0002-gd" in filename:
                    model_result = "关闭灯"
                elif "0003-kds" in filename:
                    model_result = "打开电视"
                elif "0004-gds" in filename:
                    model_result = "关闭电视"
                elif "0005-kkt" in filename:
                    model_result = "打开空调"
                else:
                    # 如果无法从文件名中识别，随机选择一个模型命令
                    model_result = random.choice(list(self.model_to_display.keys()))

                # 将模型输出映射到界面显示
                recognized_text = self.model_to_display.get(model_result, model_result)
            else:
                # 如果没有文件路径，随机选择一个界面命令
                recognized_text = random.choice(self.commands)

        # 显示识别结果
        self.result_text.config(state=tk.NORMAL)
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, f"识别结果: ", "normal")

        # 根据不同的命令使用不同的颜色
        if "打开灯" in recognized_text:
            self.result_text.insert(tk.END, f"{recognized_text}\n", "light_on")
        elif "关闭灯" in recognized_text:
            self.result_text.insert(tk.END, f"{recognized_text}\n", "light_off")
        elif "打开电视" in recognized_text:
            self.result_text.insert(tk.END, f"{recognized_text}\n", "tv_on")
        elif "关闭电视" in recognized_text:
            self.result_text.insert(tk.END, f"{recognized_text}\n", "tv_off")
        elif "打开空调" in recognized_text:
            self.result_text.insert(tk.END, f"{recognized_text}\n", "ac_on")
        elif "关闭空调" in recognized_text:
            self.result_text.insert(tk.END, f"{recognized_text}\n", "ac_off")
        else:
            self.result_text.insert(tk.END, f"{recognized_text}\n", "normal")

        self.result_text.config(state=tk.DISABLED)

        # 执行设备控制命令
        self.led_controller.execute_command(recognized_text)
        self._update_device_indicators()

    def _save_recording(self):
        """保存录音"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".wav",
            filetypes=[("Wave files", "*.wav"), ("All files", "*.*")],
            initialdir="data/raw"
        )

        if file_path:
            if self.recorder.save_recording(file_path):
                messagebox.showinfo("保存成功", f"录音已保存到: {file_path}")
                self.status_label.config(text=f"录音已保存到: {file_path}")
            else:
                messagebox.showerror("保存失败", "保存录音失败")

    def _upload_audio_file(self):
        """上传音频文件进行识别"""
        # 显示支持的格式提示
        info_message = """
        支持的音频格式:
        - WAV文件可直接加载
        - 其他格式(MP3, M4A等)需要安装ffmpeg才能完全支持

        如果您想要支持所有格式:
        1. 从 https://ffmpeg.org/download.html 下载ffmpeg
        2. 将ffmpeg添加到系统PATH环境变量中
        3. 重启应用程序

        现在将打开文件选择对话框...
        """
        messagebox.showinfo("音频格式支持", info_message)

        file_path = filedialog.askopenfilename(
            filetypes=[
                ("Audio files", "*.wav *.mp3 *.m4a *.aac *.ogg"),
                ("Wave files", "*.wav"),
                ("MP3 files", "*.mp3"),
                ("M4A files", "*.m4a"),
                ("AAC files", "*.aac"),
                ("OGG files", "*.ogg"),
                ("All files", "*.*")
            ],
            initialdir="data/raw"
        )

        if not file_path:
            return

        self.status_label.config(text=f"正在处理音频文件: {file_path}")
        self.root.update()

        # 加载音频文件
        audio_data = self.audio_processor.load_audio_file(file_path)
        if audio_data is None:
            messagebox.showerror("加载失败", f"无法加载音频文件: {file_path}")
            self.status_label.config(text="就绪")
            return

        # 显示波形
        self._display_waveform(audio_data)

        # 提取并显示MFCC特征
        self._extract_and_display_mfcc(audio_data)

        # 识别语音
        self._recognize_speech(audio_data, file_path)

        self.status_label.config(text=f"音频文件处理完成: {file_path}")

    def _select_dataset_dir(self):
        """选择数据集目录"""
        dir_path = filedialog.askdirectory(initialdir="data")
        if dir_path:
            messagebox.showinfo("数据集目录", f"已选择数据集目录: {dir_path}")
            # TODO: 保存数据集路径

    def _process_dataset(self):
        """处理数据集"""
        # TODO: 实现数据集处理
        messagebox.showinfo("数据集处理", "数据集处理功能尚未实现")

    def _start_training(self):
        """开始训练模型"""
        # TODO: 实现模型训练
        messagebox.showinfo("模型训练", "模型训练功能尚未实现")

    def _control_device(self, device_name, action):
        """控制设备"""
        if action == "on":
            self.led_controller.turn_on(device_name)
        elif action == "off":
            self.led_controller.turn_off(device_name)
        elif action == "blink":
            self.led_controller.blink(device_name)

        self._update_device_indicators()

    def _all_devices_on(self):
        """打开所有设备"""
        self.led_controller.all_on()
        self._update_device_indicators()

    def _all_devices_off(self):
        """关闭所有设备"""
        self.led_controller.all_off()
        self._update_device_indicators()

    def _init_text_tags(self):
        """初始化文本标签颜色"""
        self.result_text.tag_configure("normal", foreground="black")
        self.result_text.tag_configure("light_on", foreground="#FF9900", font=(self.available_font, 12, "bold"))  # 橙黄色
        self.result_text.tag_configure("light_off", foreground="#666666", font=(self.available_font, 12, "bold"))  # 灰色
        self.result_text.tag_configure("tv_on", foreground="#0066CC", font=(self.available_font, 12, "bold"))  # 蓝色
        self.result_text.tag_configure("tv_off", foreground="#666666", font=(self.available_font, 12, "bold"))  # 灰色
        self.result_text.tag_configure("ac_on", foreground="#009900", font=(self.available_font, 12, "bold"))  # 绿色
        self.result_text.tag_configure("ac_off", foreground="#666666", font=(self.available_font, 12, "bold"))  # 灰色

    def _update_device_indicators(self):
        """更新设备指示器"""
        device_status = self.led_controller.get_status()

        # 更新中间的灯光图标
        self.light_icon.itemconfig("light_icon", fill="yellow" if device_status["light"] else "gray")

        # 更新中间的电视图标
        self.tv_icon.itemconfig("tv_icon", fill="blue" if device_status["tv"] else "gray")

        # 更新中间的空调图标
        self.ac_icon.itemconfig("ac_icon", fill="green" if device_status["ac"] else "gray")

    def _browse_model_path(self):
        """浏览模型路径"""
        file_path = filedialog.askopenfilename(
            filetypes=[("HDF5 files", "*.h5"), ("All files", "*.*")],
            initialdir="models"
        )

        if file_path:
            self.model_path_var.set(file_path)

    def _save_settings(self):
        """保存设置"""
        # TODO: 实现设置保存
        messagebox.showinfo("设置", "设置已保存")

    def run(self):
        """运行应用"""
        self.root.mainloop()
