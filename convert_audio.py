#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
转换音频文件为WAV格式
"""

import os
import subprocess
import argparse
from tqdm import tqdm

def convert_audio_to_wav(input_file, output_file, ffmpeg_path="ffmpeg"):
    """
    使用FFmpeg将音频文件转换为WAV格式

    参数:
        input_file (str): 输入音频文件路径
        output_file (str): 输出WAV文件路径
        ffmpeg_path (str): FFmpeg可执行文件路径

    返回:
        bool: 是否成功转换
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            print(f"输入文件不存在: {input_file}")
            return False

        # 创建输出目录
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 使用librosa直接加载和保存
        import librosa
        import soundfile as sf

        try:
            # 尝试使用librosa加载音频
            audio, sr = librosa.load(input_file, sr=16000, mono=True)

            # 保存为WAV格式
            sf.write(output_file, audio, sr)

            print(f"成功转换: {input_file} -> {output_file}")
            return True
        except Exception as e:
            print(f"使用librosa转换失败: {e}")

            # 如果librosa失败，尝试使用FFmpeg
            try:
                # 构建FFmpeg命令
                command = [
                    ffmpeg_path,
                    "-i", input_file,
                    "-ar", "16000",  # 采样率16kHz
                    "-ac", "1",      # 单声道
                    "-y",            # 覆盖已有文件
                    output_file
                ]

                # 执行命令
                subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                print(f"使用FFmpeg成功转换: {input_file} -> {output_file}")
                return True
            except Exception as e2:
                print(f"使用FFmpeg转换失败: {e2}")
                return False
    except Exception as e:
        print(f"转换文件 {input_file} 失败: {e}")
        return False

def process_directory(input_dir, output_dir, ffmpeg_path="ffmpeg"):
    """
    处理目录中的所有音频文件

    参数:
        input_dir (str): 输入目录
        output_dir (str): 输出目录
        ffmpeg_path (str): FFmpeg可执行文件路径

    返回:
        int: 成功转换的文件数量
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有音频文件
    audio_files = []
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.endswith((".m4a", ".mp3", ".aac", ".flac", ".ogg")):
                audio_files.append(os.path.join(root, file))

    print(f"找到 {len(audio_files)} 个音频文件")

    # 转换所有音频文件
    success_count = 0
    for file_path in tqdm(audio_files, desc="转换音频文件"):
        # 获取相对路径
        rel_path = os.path.relpath(file_path, input_dir)

        # 创建输出文件路径
        output_path = os.path.join(output_dir, os.path.splitext(rel_path)[0] + ".wav")

        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 转换文件
        if convert_audio_to_wav(file_path, output_path, ffmpeg_path):
            success_count += 1

    return success_count

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="转换音频文件为WAV格式")

    parser.add_argument("--input_dir", type=str, default="音频",
                        help="输入音频文件目录")
    parser.add_argument("--output_dir", type=str, default="data/raw",
                        help="输出WAV文件目录")
    parser.add_argument("--ffmpeg_path", type=str, default="ffmpeg",
                        help="FFmpeg可执行文件路径")

    args = parser.parse_args()

    # 处理目录
    success_count = process_directory(args.input_dir, args.output_dir, args.ffmpeg_path)

    print(f"处理完成，成功转换 {success_count} 个文件")

if __name__ == "__main__":
    main()
