#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库查看器
用于查看和管理语音识别系统的数据库记录
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from datetime import datetime, timedelta
import json

try:
    from database.models import get_db_manager
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    print("数据库模块不可用")

class DatabaseViewer:
    """数据库查看器GUI"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("哈萨克语语音识别系统 - 数据库查看器")
        self.root.geometry("1000x700")
        
        # 初始化数据库
        self.db = None
        if DATABASE_AVAILABLE:
            try:
                self.db = get_db_manager()
                print("数据库连接成功")
            except Exception as e:
                print(f"数据库连接失败: {e}")
                messagebox.showerror("错误", f"数据库连接失败: {e}")
                return
        else:
            messagebox.showerror("错误", "数据库模块不可用")
            return
        
        # 创建GUI
        self.create_widgets()
        
        # 加载数据
        self.refresh_data()
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        title_label = ttk.Label(main_frame, text="数据库查看器", font=("SimHei", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 创建按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 刷新按钮
        refresh_btn = ttk.Button(button_frame, text="刷新数据", command=self.refresh_data)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空数据按钮
        clear_btn = ttk.Button(button_frame, text="清空数据", command=self.clear_data)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 导出数据按钮
        export_btn = ttk.Button(button_frame, text="导出数据", command=self.export_data)
        export_btn.pack(side=tk.LEFT)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个标签页
        self.create_overview_tab()
        self.create_speech_records_tab()
        self.create_device_records_tab()
        self.create_system_logs_tab()
        self.create_user_operations_tab()
        self.create_statistics_tab()
    
    def create_overview_tab(self):
        """创建概览标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="概览")
        
        # 统计信息框架
        stats_frame = ttk.LabelFrame(frame, text="系统统计", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 统计标签
        self.stats_labels = {}
        stats_info = [
            ("total_speech_records", "语音识别记录总数"),
            ("total_device_records", "设备控制记录总数"),
            ("total_system_logs", "系统日志总数"),
            ("total_user_operations", "用户操作记录总数"),
            ("recognition_accuracy", "识别准确率")
        ]
        
        for i, (key, text) in enumerate(stats_info):
            label = ttk.Label(stats_frame, text=f"{text}: 0", font=("SimHei", 12))
            label.grid(row=i//2, column=i%2, sticky=tk.W, padx=10, pady=5)
            self.stats_labels[key] = label
        
        # 最近活动框架
        recent_frame = ttk.LabelFrame(frame, text="最近活动", padding=10)
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 最近活动列表
        columns = ("时间", "类型", "内容")
        self.recent_tree = ttk.Treeview(recent_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.recent_tree.heading(col, text=col)
            self.recent_tree.column(col, width=150)
        
        # 滚动条
        recent_scrollbar = ttk.Scrollbar(recent_frame, orient=tk.VERTICAL, command=self.recent_tree.yview)
        self.recent_tree.configure(yscrollcommand=recent_scrollbar.set)
        
        self.recent_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        recent_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_speech_records_tab(self):
        """创建语音识别记录标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="语音识别记录")
        
        # 创建表格
        columns = ("ID", "时间", "来源", "预测类别", "命令", "置信度", "成功")
        self.speech_tree = ttk.Treeview(frame, columns=columns, show="headings")
        
        for col in columns:
            self.speech_tree.heading(col, text=col)
            self.speech_tree.column(col, width=100)
        
        # 滚动条
        speech_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.speech_tree.yview)
        self.speech_tree.configure(yscrollcommand=speech_scrollbar.set)
        
        self.speech_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        speech_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_device_records_tab(self):
        """创建设备控制记录标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="设备控制记录")
        
        # 创建表格
        columns = ("ID", "时间", "设备", "动作", "之前状态", "新状态")
        self.device_tree = ttk.Treeview(frame, columns=columns, show="headings")
        
        for col in columns:
            self.device_tree.heading(col, text=col)
            self.device_tree.column(col, width=120)
        
        # 滚动条
        device_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.device_tree.yview)
        self.device_tree.configure(yscrollcommand=device_scrollbar.set)
        
        self.device_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        device_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_system_logs_tab(self):
        """创建系统日志标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="系统日志")
        
        # 创建表格
        columns = ("ID", "时间", "级别", "模块", "消息")
        self.logs_tree = ttk.Treeview(frame, columns=columns, show="headings")
        
        for col in columns:
            self.logs_tree.heading(col, text=col)
            self.logs_tree.column(col, width=120)
        
        # 滚动条
        logs_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.logs_tree.yview)
        self.logs_tree.configure(yscrollcommand=logs_scrollbar.set)
        
        self.logs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        logs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_user_operations_tab(self):
        """创建用户操作记录标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="用户操作记录")
        
        # 创建表格
        columns = ("ID", "时间", "操作类型", "操作详情", "结果")
        self.operations_tree = ttk.Treeview(frame, columns=columns, show="headings")
        
        for col in columns:
            self.operations_tree.heading(col, text=col)
            self.operations_tree.column(col, width=120)
        
        # 滚动条
        operations_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.operations_tree.yview)
        self.operations_tree.configure(yscrollcommand=operations_scrollbar.set)
        
        self.operations_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        operations_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_statistics_tab(self):
        """创建统计图表标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="统计图表")
        
        # 创建图表
        self.fig, ((self.ax1, self.ax2), (self.ax3, self.ax4)) = plt.subplots(2, 2, figsize=(10, 8))
        self.fig.suptitle('系统使用统计', fontsize=16)
        
        self.canvas = FigureCanvasTkAgg(self.fig, frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def refresh_data(self):
        """刷新数据"""
        if not self.db:
            return
        
        try:
            # 更新概览统计
            self.update_overview_stats()
            
            # 更新各个表格
            self.update_speech_records()
            self.update_device_records()
            self.update_system_logs()
            self.update_user_operations()
            
            # 更新统计图表
            self.update_statistics_charts()
            
            print("数据刷新完成")
        except Exception as e:
            print(f"刷新数据失败: {e}")
            messagebox.showerror("错误", f"刷新数据失败: {e}")
    
    def update_overview_stats(self):
        """更新概览统计"""
        try:
            # 获取各表记录数
            from database.models import SpeechRecognitionRecord, DeviceControlRecord, SystemLog, UserOperation

            speech_count = self.db.session.query(SpeechRecognitionRecord).count()
            device_count = self.db.session.query(DeviceControlRecord).count()
            logs_count = self.db.session.query(SystemLog).count()
            operations_count = self.db.session.query(UserOperation).count()

            # 获取识别准确率
            accuracy = self.db.get_recognition_accuracy()

            # 更新标签
            self.stats_labels["total_speech_records"].config(text=f"语音识别记录总数: {speech_count}")
            self.stats_labels["total_device_records"].config(text=f"设备控制记录总数: {device_count}")
            self.stats_labels["total_system_logs"].config(text=f"系统日志总数: {logs_count}")
            self.stats_labels["total_user_operations"].config(text=f"用户操作记录总数: {operations_count}")
            self.stats_labels["recognition_accuracy"].config(text=f"识别准确率: {accuracy:.1f}%")
        except Exception as e:
            print(f"更新概览统计失败: {e}")
    
    def update_speech_records(self):
        """更新语音识别记录"""
        # 清空现有数据
        for item in self.speech_tree.get_children():
            self.speech_tree.delete(item)
        
        # 获取最新记录
        records = self.db.get_recent_speech_records(50)
        
        for record in records:
            self.speech_tree.insert("", "end", values=(
                record.id,
                record.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                record.source,
                record.predicted_class,
                record.command,
                f"{record.confidence:.2f}",
                "是" if record.success else "否"
            ))
    
    def update_device_records(self):
        """更新设备控制记录"""
        # 清空现有数据
        for item in self.device_tree.get_children():
            self.device_tree.delete(item)
        
        # 获取最新记录
        records = self.db.get_recent_device_records(50)
        
        for record in records:
            self.device_tree.insert("", "end", values=(
                record.id,
                record.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                record.device_label,
                record.action,
                "开启" if record.previous_state else "关闭",
                "开启" if record.new_state else "关闭"
            ))
    
    def update_system_logs(self):
        """更新系统日志"""
        try:
            # 清空现有数据
            for item in self.logs_tree.get_children():
                self.logs_tree.delete(item)

            # 获取最新日志
            from database.models import SystemLog
            logs = self.db.session.query(SystemLog).order_by(SystemLog.timestamp.desc()).limit(50).all()

            for log in logs:
                self.logs_tree.insert("", "end", values=(
                    log.id,
                    log.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                    log.level,
                    log.module,
                    log.message
                ))
        except Exception as e:
            print(f"更新系统日志失败: {e}")
    
    def update_user_operations(self):
        """更新用户操作记录"""
        try:
            # 清空现有数据
            for item in self.operations_tree.get_children():
                self.operations_tree.delete(item)

            # 获取最新操作记录
            from database.models import UserOperation
            operations = self.db.session.query(UserOperation).order_by(UserOperation.timestamp.desc()).limit(50).all()

            for op in operations:
                self.operations_tree.insert("", "end", values=(
                    op.id,
                    op.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                    op.operation_type,
                    op.operation_details if op.operation_details else "",
                    op.result
                ))
        except Exception as e:
            print(f"更新用户操作记录失败: {e}")
    
    def update_statistics_charts(self):
        """更新统计图表"""
        try:
            # 清空图表
            for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
                ax.clear()

            # 设备使用统计
            device_stats = self.db.get_device_statistics()
            if device_stats:
                devices = list(device_stats.keys())
                counts = list(device_stats.values())
                device_names = {'light': '灯光', 'tv': '电视', 'ac': '空调'}
                devices_cn = [device_names.get(d, d) for d in devices]

                self.ax1.bar(devices_cn, counts)
                self.ax1.set_title('设备使用统计')
                self.ax1.set_ylabel('使用次数')

            # 简单的统计图表
            from database.models import SpeechRecognitionRecord
            from sqlalchemy import func

            # 识别来源统计
            speech_sources = self.db.session.query(
                SpeechRecognitionRecord.source,
                func.count(SpeechRecognitionRecord.id)
            ).group_by(SpeechRecognitionRecord.source).all()

            if speech_sources:
                sources, counts = zip(*speech_sources)
                self.ax2.pie(counts, labels=sources, autopct='%1.1f%%')
                self.ax2.set_title('语音识别来源分布')

            # 显示简单的统计信息
            total_records = self.db.session.query(SpeechRecognitionRecord).count()
            self.ax3.text(0.5, 0.5, f'总记录数: {total_records}',
                         horizontalalignment='center', verticalalignment='center',
                         transform=self.ax3.transAxes, fontsize=16)
            self.ax3.set_title('记录统计')
            self.ax3.axis('off')

            # 识别准确率
            accuracy = self.db.get_recognition_accuracy()
            self.ax4.text(0.5, 0.5, f'识别准确率: {accuracy:.1f}%',
                         horizontalalignment='center', verticalalignment='center',
                         transform=self.ax4.transAxes, fontsize=16)
            self.ax4.set_title('识别准确率')
            self.ax4.axis('off')

            self.fig.tight_layout()
            self.canvas.draw()
        except Exception as e:
            print(f"更新统计图表失败: {e}")
    
    def clear_data(self):
        """清空数据"""
        if messagebox.askyesno("确认", "确定要清空所有数据吗？此操作不可恢复！"):
            try:
                # 清空所有表
                self.db.session.execute("DELETE FROM speech_recognition_records")
                self.db.session.execute("DELETE FROM device_control_records")
                self.db.session.execute("DELETE FROM system_logs")
                self.db.session.execute("DELETE FROM user_operations")
                self.db.session.commit()
                
                messagebox.showinfo("成功", "数据已清空")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("错误", f"清空数据失败: {e}")
    
    def export_data(self):
        """导出数据"""
        messagebox.showinfo("提示", "导出功能待实现")

if __name__ == "__main__":
    if not DATABASE_AVAILABLE:
        print("数据库模块不可用，请先安装依赖")
        exit(1)
    
    root = tk.Tk()
    app = DatabaseViewer(root)
    root.mainloop()
