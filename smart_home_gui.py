#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
哈萨克语语音识别智能家居控制系统 - 简化版
"""

import os
import sys
import time
import math
import json
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
from PIL import Image, ImageTk, ImageDraw, ImageFilter, ImageEnhance
import joblib
import librosa
import colorsys
import pyaudio
import wave
import threading
import queue
import struct

# 导入数据库模块
try:
    from database.models import get_db_manager, close_db
    DATABASE_AVAILABLE = True
    print("数据库模块加载成功")
except ImportError as e:
    print(f"数据库模块加载失败: {e}")
    DATABASE_AVAILABLE = False

# 设置matplotlib字体
matplotlib.use("TkAgg")
plt.rcParams['font.sans-serif'] = ['SimHei', 'SimSun', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 强制使用SimHei字体
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['axes.unicode_minus'] = False

class SmartHomeGUI:
    """智能家居语音控制系统GUI类"""

    def __init__(self, root):
        """初始化GUI"""
        print("初始化智能家居GUI应用...")
        self.root = root
        self.root.title("哈萨克语语音识别智能家居控制系统 - 3D版")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # 设置主题颜色 - 更亮的配色
        self.theme_color = "#e8f4fc"  # 浅蓝色背景
        self.accent_color = "#3498db"  # 亮蓝色
        self.success_color = "#2ecc71"  # 绿色
        self.warning_color = "#f39c12"  # 橙色
        self.error_color = "#e74c3c"    # 红色
        self.text_color = "#2c3e50"     # 深蓝灰色文字
        self.wall_color = "#b3d9ff"     # 墙壁颜色
        self.floor_color = "#d9e6f2"    # 地板颜色

        # 设置字体
        self.setup_fonts()

        # 设置样式
        self.setup_styles()

        # 创建GUI组件
        self.create_widgets()

        # 加载模型
        self.load_model()

        # 初始化录音参数
        self.sample_rate = 16000
        self.is_recording = False
        self.auto_listening = False
        self.chunk_size = 1024
        self.format = pyaudio.paInt16
        self.channels = 1

        # 初始化PyAudio
        self.audio = pyaudio.PyAudio()

        # 创建音频队列
        self.audio_queue = queue.Queue()

        # 录音线程
        self.recording_thread = None
        self.listening_thread = None

        # 录音数据
        self.frames = []

        # 设备状态
        self.devices = {
            "light": False,  # 灯
            "tv": False,     # 电视
            "ac": False      # 空调
        }

        # 设备图标缓存
        self.device_icons = {}

        # 初始化数据库
        self.db = None
        if DATABASE_AVAILABLE:
            try:
                self.db = get_db_manager()
                self.log_system_event('INFO', 'DATABASE', '数据库连接成功', '成功连接到SQLite数据库')
                self.log_user_operation('启动系统', '用户启动智能家居语音控制系统')
                print("数据库初始化成功")
            except Exception as e:
                print(f"数据库初始化失败: {e}")
                self.db = None
                self.log_system_event('ERROR', 'DATABASE', '数据库连接失败', str(e))
        else:
            print("数据库功能不可用")

        # 命令映射
        self.commands = {
            '0001-kd': "打开灯",
            '0002-gd': "关闭灯",
            '0003-kds': "打开电视",
            '0004-gds': "关闭电视",
            '0005-kkt': "打开空调"
        }

        # 动作映射
        self.actions = {
            "打开灯": ("light", True),
            "关闭灯": ("light", False),
            "打开电视": ("tv", True),
            "关闭电视": ("tv", False),
            "打开空调": ("ac", True),
            "关闭空调": ("ac", False)
        }

        # 3D效果设置
        self.shadow_offset = 5
        self.light_angle = 45  # 光源角度
        self.light_intensity = 0.8  # 光源强度
        self.animation_speed = 10  # 动画速度

        # 更新状态
        self.update_status("系统就绪")
        self.log_system_event('INFO', 'GUI', '系统启动成功', '智能家居语音控制系统已成功启动')
        print("GUI应用创建成功，开始运行...")

    def setup_fonts(self):
        """设置字体"""
        try:
            self.font_family = "SimSun"
            self.title_font = (self.font_family, 18, "bold")
            self.normal_font = (self.font_family, 12)
            self.small_font = (self.font_family, 10)
            self.button_font = (self.font_family, 12, "bold")
            self.device_font = (self.font_family, 14, "bold")
            print(f"成功加载字体: {self.font_family}")
            print(f"GUI使用字体: {self.font_family}")
        except Exception as e:
            print(f"加载字体失败: {e}")
            self.font_family = "TkDefaultFont"
            self.title_font = (self.font_family, 18, "bold")
            self.normal_font = (self.font_family, 12)
            self.small_font = (self.font_family, 10)
            self.button_font = (self.font_family, 12, "bold")
            self.device_font = (self.font_family, 14, "bold")

    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()

        # 配置主题
        style.configure("TFrame", background=self.theme_color)
        style.configure("TLabel", background=self.theme_color, foreground=self.text_color, font=self.normal_font)
        style.configure("TLabelframe", background=self.theme_color, foreground=self.text_color, font=self.normal_font)
        style.configure("TLabelframe.Label", background=self.theme_color, foreground=self.text_color, font=self.title_font)

        # 按钮样式
        style.configure("TButton",
                        background=self.accent_color,
                        foreground=self.text_color,
                        font=self.button_font,
                        padding=10,
                        relief="raised")

        # 按钮悬停效果
        style.map("TButton",
                 background=[("active", self.accent_color), ("pressed", "#2980b9")],
                 relief=[("pressed", "sunken")])

        # 录音按钮样式
        style.configure("Record.TButton",
                        background=self.warning_color,
                        foreground=self.text_color)

        # 自动监听按钮样式
        style.configure("Listen.TButton",
                        background=self.success_color,
                        foreground=self.text_color)

        # 状态栏样式 - 更亮的颜色
        style.configure("Status.TLabel",
                        background="#d0e4f5",
                        foreground=self.text_color,
                        font=self.small_font,
                        padding=5)

        # 设置根窗口背景
        self.root.configure(background=self.theme_color)

    def create_widgets(self):
        """创建GUI组件"""
        print("创建GUI组件...")

        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        print("主框架创建完成")

        # 创建控制面板
        self.create_control_panel()

        # 创建房间显示区域
        self.create_room_display()

        # 创建状态栏
        self.create_status_bar()

        print("所有GUI组件创建完成")

    def create_control_panel(self):
        """创建控制面板"""
        print("创建控制面板...")

        # 控制面板框架
        self.control_frame = ttk.LabelFrame(self.main_frame, text="语音控制中心", padding=15)
        self.control_frame.pack(fill=tk.X, padx=10, pady=10)

        # 按钮框架
        button_frame = ttk.Frame(self.control_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # 上传音频按钮
        self.upload_button = ttk.Button(
            button_frame,
            text="上传音频文件",
            command=self.upload_audio,
            style="TButton"
        )
        self.upload_button.pack(side=tk.LEFT, padx=10, pady=10)

        # 开始录音按钮
        self.record_button = ttk.Button(
            button_frame,
            text="开始录音",
            command=self.toggle_recording,
            style="Record.TButton"
        )
        self.record_button.pack(side=tk.LEFT, padx=10, pady=10)

        # 自动监听按钮
        self.auto_listen_button = ttk.Button(
            button_frame,
            text="自动语音监听",
            command=self.toggle_auto_listening,
            style="Listen.TButton"
        )
        self.auto_listen_button.pack(side=tk.LEFT, padx=10, pady=10)

        # 状态指示器
        status_frame = ttk.Frame(button_frame)
        status_frame.pack(side=tk.RIGHT, padx=10, pady=10)

        # 模型状态指示器
        self.model_status = ttk.Label(
            status_frame,
            text="模型状态: 未加载",
            font=self.normal_font
        )
        self.model_status.pack(side=tk.TOP, anchor=tk.E)

        # 识别状态指示器
        self.recognition_status = ttk.Label(
            status_frame,
            text="识别状态: 就绪",
            font=self.normal_font
        )
        self.recognition_status.pack(side=tk.TOP, anchor=tk.E)

        # 波形图
        self.waveform_frame = ttk.LabelFrame(self.control_frame, text="音频波形分析", padding=15)
        self.waveform_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建波形图
        self.waveform_figure = plt.Figure(figsize=(6, 2), dpi=100)
        self.waveform_subplot = self.waveform_figure.add_subplot(111)
        self.waveform_subplot.set_facecolor("#f0f8ff")  # 淡蓝色背景
        self.waveform_figure.patch.set_facecolor("#f0f8ff")

        # 设置波形图样式 - 更亮的颜色
        self.waveform_subplot.tick_params(axis='x', colors=self.text_color)
        self.waveform_subplot.tick_params(axis='y', colors=self.text_color)
        self.waveform_subplot.spines['bottom'].set_color(self.text_color)
        self.waveform_subplot.spines['top'].set_color(self.text_color)
        self.waveform_subplot.spines['left'].set_color(self.text_color)
        self.waveform_subplot.spines['right'].set_color(self.text_color)

        # 设置更亮的背景色
        self.waveform_subplot.set_facecolor("#f0f8ff")  # 淡蓝色背景
        self.waveform_figure.patch.set_facecolor("#f0f8ff")

        self.waveform_canvas = FigureCanvasTkAgg(self.waveform_figure, self.waveform_frame)
        self.waveform_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        print("控制面板创建完成")

    def create_room_display(self):
        """创建房间显示区域"""
        print("创建房间显示区域...")

        # 房间显示框架
        self.room_frame = ttk.LabelFrame(self.main_frame, text="智能家居3D控制中心", padding=15)
        self.room_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建画布
        self.room_canvas = tk.Canvas(self.room_frame, bg=self.theme_color, highlightthickness=0)
        self.room_canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定调整大小事件
        self.room_canvas.bind("<Configure>", self.draw_room)

        # 创建设备控制面板
        control_panel = ttk.Frame(self.room_frame)
        control_panel.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        # 设备状态标签
        self.light_status = ttk.Label(control_panel, text="灯光: 关闭", font=self.normal_font)
        self.light_status.pack(side=tk.LEFT, padx=20, pady=5)

        self.tv_status = ttk.Label(control_panel, text="电视: 关闭", font=self.normal_font)
        self.tv_status.pack(side=tk.LEFT, padx=20, pady=5)

        self.ac_status = ttk.Label(control_panel, text="空调: 关闭", font=self.normal_font)
        self.ac_status.pack(side=tk.LEFT, padx=20, pady=5)

        # 最近识别结果
        self.last_command = ttk.Label(
            control_panel,
            text="最近识别: 无",
            font=self.normal_font
        )
        self.last_command.pack(side=tk.RIGHT, padx=20, pady=5)

        print("房间显示区域创建完成")

    def create_status_bar(self):
        """创建状态栏"""
        print("创建状态栏...")

        # 状态栏
        self.status_bar = ttk.Label(
            self.root,
            text="系统就绪",
            relief=tk.SUNKEN,
            anchor=tk.W,
            font=self.small_font,
            style="Status.TLabel"
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 创建时间显示
        self.time_display = ttk.Label(
            self.status_bar,
            text=time.strftime("%Y-%m-%d %H:%M:%S"),
            font=self.small_font,
            style="Status.TLabel"
        )
        self.time_display.pack(side=tk.RIGHT, padx=10)

        # 更新时间
        self.update_time()

        print("状态栏创建完成")

    def update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        self.time_display.config(text=current_time)
        self.root.after(1000, self.update_time)  # 每秒更新一次

    def load_model(self):
        """加载模型"""
        try:
            # 加载SVM模型
            model_path = os.path.join("models", "kazakh_speech_svm_model.joblib")

            if os.path.exists(model_path):
                self.model = joblib.load(model_path)
                print(f"已加载模型: {model_path}")
                print(f"模型类型: {type(self.model)}")
                print(f"模型类别: {self.model.classes_}")

                # 更新模型状态
                if hasattr(self, 'model_status'):
                    self.model_status.config(
                        text=f"模型状态: 已加载 ({len(self.model.classes_)}个类别)",
                        foreground=self.success_color
                    )

                self.update_status("模型加载成功")
                self.log_system_event('INFO', 'MODEL', '模型加载成功', f'哈萨克语语音识别模型加载完成，支持{len(self.model.classes_)}个类别')
            else:
                print(f"错误: 模型文件不存在: {model_path}")
                self.model = None

                # 更新模型状态
                if hasattr(self, 'model_status'):
                    self.model_status.config(
                        text="模型状态: 加载失败",
                        foreground=self.error_color
                    )

                self.update_status("模型加载失败")
        except Exception as e:
            print(f"加载模型失败: {e}")
            import traceback
            traceback.print_exc()
            self.model = None

            # 更新模型状态
            if hasattr(self, 'model_status'):
                self.model_status.config(
                    text=f"模型状态: 加载错误 ({str(e)})",
                    foreground=self.error_color
                )

            self.update_status("模型加载失败")

    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
        self.root.update_idletasks()

    def toggle_recording(self):
        """切换录音状态"""
        if self.is_recording:
            # 停止录音
            self.is_recording = False
            self.record_button.config(text="开始录音")
            self.update_status("录音已停止")
            self.log_user_operation('停止录音', '用户停止录音')

            # 停止录音线程
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=1.0)

            # 处理录音数据
            if self.frames:
                try:
                    # 将录音数据转换为numpy数组
                    audio_data = np.frombuffer(b''.join(self.frames), dtype=np.int16).astype(np.float32) / 32768.0

                    # 打印音频统计信息，帮助调试
                    print(f"录音音频统计: 长度={len(audio_data)}, 最大值={np.max(audio_data):.4f}, 最小值={np.min(audio_data):.4f}, 均值={np.mean(audio_data):.4f}, 标准差={np.std(audio_data):.4f}")

                    # 使用librosa重新加载音频，确保与上传文件处理方式一致
                    # 首先保存为临时WAV文件
                    temp_wav = "temp_recording.wav"
                    with wave.open(temp_wav, 'wb') as wf:
                        wf.setnchannels(self.channels)
                        wf.setsampwidth(self.audio.get_sample_size(self.format))
                        wf.setframerate(self.sample_rate)
                        wf.writeframes(b''.join(self.frames))

                    # 使用librosa加载，与上传文件处理方式一致
                    audio_data, sr = librosa.load(temp_wav, sr=self.sample_rate)
                    print(f"重新加载后的音频统计: 长度={len(audio_data)}, 最大值={np.max(audio_data):.4f}, 最小值={np.min(audio_data):.4f}, 均值={np.mean(audio_data):.4f}, 标准差={np.std(audio_data):.4f}")

                    # 裁剪音频，去除静音部分
                    audio_data = self.trim_silence(audio_data)
                    print(f"裁剪后的音频统计: 长度={len(audio_data)}, 最大值={np.max(audio_data):.4f}, 最小值={np.min(audio_data):.4f}, 均值={np.mean(audio_data):.4f}, 标准差={np.std(audio_data):.4f}")

                    # 显示波形
                    self.plot_waveform(audio_data)

                    # 识别语音
                    self.recognize_speech(audio_data, source="录音")

                    # 清空录音数据
                    self.frames = []
                except Exception as e:
                    print(f"处理录音数据失败: {e}")
                    self.update_status(f"处理录音数据失败: {e}")
        else:
            # 开始录音
            self.is_recording = True
            self.record_button.config(text="停止录音")
            self.update_status("正在录音...")
            self.log_user_operation('开始录音', '用户开始录音进行语音识别')

            # 清空录音数据
            self.frames = []

            # 启动录音线程
            self.recording_thread = threading.Thread(target=self.record_audio)
            self.recording_thread.daemon = True
            self.recording_thread.start()

    def record_audio(self):
        """录音线程"""
        try:
            # 打开音频流
            stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )

            print("开始录音...")

            # 录音
            while self.is_recording:
                data = stream.read(self.chunk_size)
                self.frames.append(data)

            # 关闭音频流
            stream.stop_stream()
            stream.close()

            print("录音结束")
        except Exception as e:
            print(f"录音出错: {e}")
            self.is_recording = False
            self.record_button.config(text="开始录音")
            self.update_status(f"录音出错: {e}")

    def toggle_auto_listening(self):
        """切换自动监听状态"""
        if self.auto_listening:
            # 停止自动监听
            self.auto_listening = False
            self.auto_listen_button.config(text="自动语音监听")
            self.update_status("自动语音监听已关闭")
            self.log_user_operation('停止自动监听', '用户停止自动语音监听功能')

            # 停止自动监听线程
            if self.listening_thread and self.listening_thread.is_alive():
                self.listening_thread.join(timeout=1.0)
        else:
            # 开始自动监听
            self.auto_listening = True
            self.auto_listen_button.config(text="停止自动监听")
            self.update_status("自动语音监听已开启")
            self.log_user_operation('开始自动监听', '用户开启自动语音监听功能')

            # 启动自动监听线程
            self.listening_thread = threading.Thread(target=self.auto_listen)
            self.listening_thread.daemon = True
            self.listening_thread.start()

    def auto_listen(self):
        """自动监听线程"""
        try:
            # 打开音频流
            stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )

            print("开始自动监听...")

            # 初始化变量
            silence_threshold = 0.01  # 静音阈值
            min_audio_length = 0.5  # 最小音频长度（秒）
            max_audio_length = 3.0  # 最大音频长度（秒）
            is_speaking = False
            speech_frames = []
            silence_count = 0
            max_silence = int(0.5 * self.sample_rate / self.chunk_size)  # 0.5秒静音

            # 监听
            while self.auto_listening:
                # 读取音频数据
                data = stream.read(self.chunk_size)
                audio_chunk = np.frombuffer(data, dtype=np.int16).astype(np.float32) / 32768.0

                # 计算音量
                volume = np.abs(audio_chunk).mean()

                # 检测语音
                if volume > silence_threshold and not is_speaking:
                    # 开始说话
                    is_speaking = True
                    speech_frames = [data]
                    silence_count = 0
                    print("检测到语音...")
                elif volume > silence_threshold and is_speaking:
                    # 继续说话
                    speech_frames.append(data)
                    silence_count = 0
                elif volume <= silence_threshold and is_speaking:
                    # 可能停止说话
                    silence_count += 1
                    speech_frames.append(data)

                    # 检查是否停止说话
                    if silence_count >= max_silence:
                        # 停止说话
                        is_speaking = False

                        # 检查音频长度
                        audio_length = len(speech_frames) * self.chunk_size / self.sample_rate
                        if audio_length >= min_audio_length and audio_length <= max_audio_length:
                            print(f"检测到语音片段，长度: {audio_length:.2f}秒")

                            # 处理语音
                            try:
                                # 将录音数据转换为numpy数组
                                audio_data = np.frombuffer(b''.join(speech_frames), dtype=np.int16).astype(np.float32) / 32768.0

                                # 打印音频统计信息，帮助调试
                                print(f"自动监听音频统计: 长度={len(audio_data)}, 最大值={np.max(audio_data):.4f}, 最小值={np.min(audio_data):.4f}, 均值={np.mean(audio_data):.4f}, 标准差={np.std(audio_data):.4f}")

                                # 使用librosa重新加载音频，确保与上传文件处理方式一致
                                # 首先保存为临时WAV文件
                                temp_wav = "temp_listening.wav"
                                with wave.open(temp_wav, 'wb') as wf:
                                    wf.setnchannels(self.channels)
                                    wf.setsampwidth(self.audio.get_sample_size(self.format))
                                    wf.setframerate(self.sample_rate)
                                    wf.writeframes(b''.join(speech_frames))

                                # 使用librosa加载，与上传文件处理方式一致
                                audio_data, sr = librosa.load(temp_wav, sr=self.sample_rate)
                                print(f"重新加载后的音频统计: 长度={len(audio_data)}, 最大值={np.max(audio_data):.4f}, 最小值={np.min(audio_data):.4f}, 均值={np.mean(audio_data):.4f}, 标准差={np.std(audio_data):.4f}")

                                # 裁剪音频，去除静音部分
                                audio_data = self.trim_silence(audio_data)
                                print(f"裁剪后的音频统计: 长度={len(audio_data)}, 最大值={np.max(audio_data):.4f}, 最小值={np.min(audio_data):.4f}, 均值={np.mean(audio_data):.4f}, 标准差={np.std(audio_data):.4f}")

                                # 显示波形
                                self.plot_waveform(audio_data)

                                # 识别语音
                                self.recognize_speech(audio_data, source="自动监听")
                            except Exception as e:
                                print(f"处理语音数据失败: {e}")

                        # 重置
                        speech_frames = []

                # 防止CPU占用过高
                time.sleep(0.01)

            # 关闭音频流
            stream.stop_stream()
            stream.close()

            print("自动监听结束")
        except Exception as e:
            print(f"自动监听出错: {e}")
            self.auto_listening = False
            self.auto_listen_button.config(text="自动语音监听")
            self.update_status(f"自动监听出错: {e}")

    def upload_audio(self):
        """上传音频文件"""
        file_path = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=[("音频文件", "*.wav *.mp3 *.m4a")]
        )

        if not file_path:
            return

        self.update_status(f"正在处理音频文件: {file_path}")
        self.log_user_operation('上传音频', f'用户上传音频文件: {os.path.basename(file_path)}')

        try:
            # 加载音频文件
            audio_data, sr = librosa.load(file_path, sr=self.sample_rate)

            # 打印音频统计信息，帮助调试
            print(f"上传音频统计: 长度={len(audio_data)}, 最大值={np.max(audio_data):.4f}, 最小值={np.min(audio_data):.4f}, 均值={np.mean(audio_data):.4f}, 标准差={np.std(audio_data):.4f}")

            # 裁剪音频，去除静音部分
            audio_data = self.trim_silence(audio_data)
            print(f"裁剪后的音频统计: 长度={len(audio_data)}, 最大值={np.max(audio_data):.4f}, 最小值={np.min(audio_data):.4f}, 均值={np.mean(audio_data):.4f}, 标准差={np.std(audio_data):.4f}")

            # 显示波形
            self.plot_waveform(audio_data)

            # 识别语音
            self.recognize_speech(audio_data, source="上传", file_path=file_path)

        except Exception as e:
            print(f"处理音频文件失败: {e}")
            self.update_status(f"处理音频文件失败: {e}")

    def plot_waveform(self, audio_data):
        """绘制波形图"""
        self.waveform_subplot.clear()

        # 设置背景色 - 更亮的颜色
        self.waveform_subplot.set_facecolor("#f0f8ff")  # 淡蓝色背景

        # 计算时间轴
        time_axis = np.arange(0, len(audio_data)) / self.sample_rate

        # 绘制波形
        self.waveform_subplot.plot(
            time_axis,
            audio_data,
            color=self.accent_color,
            linewidth=1.5,
            alpha=0.8
        )

        # 添加填充
        self.waveform_subplot.fill_between(
            time_axis,
            audio_data,
            0,
            where=(audio_data > 0),
            color=self.accent_color,
            alpha=0.3
        )
        self.waveform_subplot.fill_between(
            time_axis,
            audio_data,
            0,
            where=(audio_data < 0),
            color=self.warning_color,
            alpha=0.3
        )

        # 设置标签 - 使用SimHei字体
        self.waveform_subplot.set_xlabel("时间 (秒)", color=self.text_color, fontproperties='SimHei')
        self.waveform_subplot.set_ylabel("振幅", color=self.text_color, fontproperties='SimHei')
        self.waveform_subplot.set_title("音频波形分析", color=self.text_color, fontsize=12, fontweight='bold', fontproperties='SimHei')

        # 设置网格
        self.waveform_subplot.grid(True, linestyle='--', alpha=0.3, color=self.text_color)

        # 设置刻度颜色
        self.waveform_subplot.tick_params(axis='x', colors=self.text_color)
        self.waveform_subplot.tick_params(axis='y', colors=self.text_color)

        # 设置边框颜色
        for spine in self.waveform_subplot.spines.values():
            spine.set_color(self.text_color)

        # 应用布局
        self.waveform_figure.tight_layout()
        self.waveform_canvas.draw()

    def recognize_speech(self, audio_data, source="未知", file_path=None):
        """识别语音"""
        if self.model is None:
            print("错误: 模型未加载")
            self.update_status("错误: 模型未加载")

            # 更新识别状态
            if hasattr(self, 'recognition_status'):
                self.recognition_status.config(
                    text="识别状态: 模型未加载",
                    foreground=self.error_color
                )

            return

        try:
            # 更新识别状态
            if hasattr(self, 'recognition_status'):
                self.recognition_status.config(
                    text=f"识别状态: 正在识别... ({source})",
                    foreground=self.warning_color
                )

            # 简单的音频预处理
            print(f"处理来源: {source} 的音频")

            # 提取特征
            features = self.extract_features(audio_data)

            # 打印特征形状和统计信息，帮助调试
            print(f"特征形状: {features.shape}")
            print(f"特征统计: 最大值={np.max(features):.4f}, 最小值={np.min(features):.4f}, 均值={np.mean(features):.4f}, 标准差={np.std(features):.4f}")

            # 预测
            predicted_class = self.model.predict(features)[0]

            # 获取所有类别的概率
            probabilities = self.model.predict_proba(features)[0]

            # 打印所有类别的概率，帮助调试
            print(f"所有类别概率:")
            for i, class_name in enumerate(self.model.classes_):
                print(f"  {class_name}: {probabilities[i]:.4f}")

            # 使用原始概率，不做任何调整
            predicted_class = self.model.predict(features)[0]
            class_index = list(self.model.classes_).index(predicted_class)
            confidence = probabilities[class_index]





            # 获取命令
            command = self.commands.get(predicted_class, "未知命令")

            print(f"来源: {source}")
            print(f"原始预测: {predicted_class}")
            print(f"预测: {predicted_class} -> {command}, 置信度={confidence:.2f}")

            # 记录语音识别结果到数据库
            audio_stats = {
                'max_value': float(np.max(audio_data)),
                'min_value': float(np.min(audio_data)),
                'mean': float(np.mean(audio_data)),
                'std': float(np.std(audio_data)),
                'length': len(audio_data)
            }

            speech_record_id = self.log_speech_recognition(
                source=source,
                predicted_class=predicted_class,
                command=command,
                confidence=confidence,
                file_path=file_path,
                audio_length=len(audio_data) / self.sample_rate,
                audio_stats=audio_stats,
                success=True
            )

            # 更新识别状态
            if hasattr(self, 'recognition_status'):
                self.recognition_status.config(
                    text=f"识别状态: 成功 ({confidence:.2f})",
                    foreground=self.success_color
                )

            # 更新最近识别结果
            if hasattr(self, 'last_command'):
                self.last_command.config(
                    text=f"最近识别: {command}",
                    foreground=self.accent_color
                )

            self.update_status(f"预测结果: {command} (置信度: {confidence:.2f})")

            # 执行命令
            self.execute_command(command, speech_record_id)

        except Exception as e:
            print(f"预测出错: {e}")
            import traceback
            traceback.print_exc()

            # 更新识别状态
            if hasattr(self, 'recognition_status'):
                self.recognition_status.config(
                    text=f"识别状态: 失败 ({str(e)[:20]}...)",
                    foreground=self.error_color
                )

            self.update_status(f"预测出错: {e}")

    def extract_features(self, audio_data, n_mfcc=13):
        """提取MFCC特征"""
        # 确保音频长度适合特征提取
        if len(audio_data) < self.sample_rate * 0.1:  # 至少0.1秒
            # 如果音频太短，用零填充
            audio_data = np.pad(audio_data, (0, self.sample_rate - len(audio_data)), 'constant')

        # 提取MFCC特征 - 使用与训练时相同的参数
        mfccs = librosa.feature.mfcc(
            y=audio_data,
            sr=self.sample_rate,
            n_mfcc=n_mfcc,
            n_fft=2048,
            hop_length=512
        )

        # 打印MFCC形状，帮助调试
        print(f"MFCC形状: {mfccs.shape}")

        # 计算MFCC特征的统计量
        mfccs_mean = np.mean(mfccs, axis=1)
        mfccs_std = np.std(mfccs, axis=1)
        mfccs_max = np.max(mfccs, axis=1)
        mfccs_min = np.min(mfccs, axis=1)

        # 组合特征
        features = np.concatenate((mfccs_mean, mfccs_std, mfccs_max, mfccs_min))

        # 重塑特征以适应SVM模型输入
        features = features.reshape(1, -1)

        return features

    def execute_command(self, command, speech_record_id=None):
        """执行命令"""
        device, state = self.actions.get(command, (None, None))

        if device is None:
            return

        # 记录之前的状态
        previous_state = self.devices[device]

        # 更新设备状态
        self.devices[device] = state

        # 设备中文名称映射
        device_labels = {
            'light': '灯光',
            'tv': '电视',
            'ac': '空调'
        }
        device_label = device_labels.get(device, device)
        action = '开启' if state else '关闭'

        # 记录设备控制到数据库
        self.log_device_control(
            device_name=device,
            device_label=device_label,
            action=action,
            previous_state=previous_state,
            new_state=state,
            speech_record_id=speech_record_id
        )

        # 更新显示
        self.draw_room()

        # 显示状态变化
        if device == "light":
            if state:
                print("灯光已打开")
                self.log_system_event('INFO', 'DEVICE', '灯光开启', '用户通过语音命令开启灯光')
            else:
                print("灯光已关闭")
                self.log_system_event('INFO', 'DEVICE', '灯光关闭', '用户通过语音命令关闭灯光')
        elif device == "tv":
            if state:
                print("电视已打开")
                self.log_system_event('INFO', 'DEVICE', '电视开启', '用户通过语音命令开启电视')
            else:
                print("电视已关闭")
                self.log_system_event('INFO', 'DEVICE', '电视关闭', '用户通过语音命令关闭电视')
        elif device == "ac":
            if state:
                print("空调已打开")
                self.log_system_event('INFO', 'DEVICE', '空调开启', '用户通过语音命令开启空调')
            else:
                print("空调已关闭")
                self.log_system_event('INFO', 'DEVICE', '空调关闭', '用户通过语音命令关闭空调')

    def draw_room(self, event=None):
        """绘制房间"""
        # 获取画布尺寸
        if event:
            width = event.width
            height = event.height
        else:
            width = self.room_canvas.winfo_width()
            height = self.room_canvas.winfo_height()

        print(f"绘制房间: 画布尺寸 = {width} x {height}")

        # 清空画布
        self.room_canvas.delete("all")

        # 绘制房间背景 - 3D效果
        self.draw_3d_room(width, height)

        # 绘制设备
        self.draw_devices(width, height)

        # 更新设备状态标签
        self.update_device_status_labels()

    def draw_3d_room(self, width, height):
        """绘制3D房间背景"""
        # 房间地板
        floor_points = [
            20, height-20,                # 左下
            width-20, height-20,          # 右下
            width-60, height-100,         # 右上
            60, height-100                # 左上
        ]
        self.room_canvas.create_polygon(
            floor_points,
            fill=self.floor_color,
            outline="#a0c0e0",
            width=2
        )

        # 左墙
        left_wall_points = [
            20, height-20,                # 左下
            60, height-100,               # 左上
            60, 100,                      # 右上
            20, 20                        # 右下
        ]
        self.room_canvas.create_polygon(
            left_wall_points,
            fill=self.wall_color,
            outline="#a0c0e0",
            width=2
        )

        # 右墙
        right_wall_points = [
            width-20, height-20,          # 右下
            width-20, 20,                 # 右上
            width-60, 100,                # 左上
            width-60, height-100          # 左下
        ]
        self.room_canvas.create_polygon(
            right_wall_points,
            fill=self.wall_color,
            outline="#a0c0e0",
            width=2
        )

        # 后墙
        back_wall_points = [
            60, 100,                      # 左下
            width-60, 100,                # 右下
            width-20, 20,                 # 右上
            20, 20                        # 左上
        ]
        self.room_canvas.create_polygon(
            back_wall_points,
            fill="#c4e0ff",
            outline="#a0c0e0",
            width=2
        )

    def update_device_status_labels(self):
        """更新设备状态标签"""
        # 更新灯光状态
        if self.devices["light"]:
            self.light_status.config(
                text="灯光: 开启",
                foreground=self.success_color
            )
        else:
            self.light_status.config(
                text="灯光: 关闭",
                foreground=self.text_color
            )

        # 更新电视状态
        if self.devices["tv"]:
            self.tv_status.config(
                text="电视: 开启",
                foreground=self.success_color
            )
        else:
            self.tv_status.config(
                text="电视: 关闭",
                foreground=self.text_color
            )

        # 更新空调状态
        if self.devices["ac"]:
            self.ac_status.config(
                text="空调: 开启",
                foreground=self.success_color
            )
        else:
            self.ac_status.config(
                text="空调: 关闭",
                foreground=self.text_color
            )

    def draw_devices(self, width, height):
        """绘制设备"""
        # 计算设备位置
        center_x = width / 2
        center_y = height / 2
        radius = min(width, height) / 4

        # 确保半径不为零
        if radius < 50:
            radius = 50

        # 灯的位置 - 中央顶部
        light_x = center_x
        light_y = center_y - radius * 1.0

        # 电视的位置 - 右侧
        tv_x = center_x + radius * 1.2
        tv_y = center_y

        # 空调的位置 - 左侧
        ac_x = center_x - radius * 1.2
        ac_y = center_y

        # 绘制灯 - 更大的尺寸
        self.draw_3d_light(light_x, light_y, 50)

        # 绘制电视 - 更大的尺寸
        self.draw_3d_tv(tv_x, tv_y, 60)

        # 绘制空调 - 更大的尺寸
        self.draw_3d_ac(ac_x, ac_y, 50)

    def create_3d_effect(self, image, light_angle=45, intensity=0.8):
        """创建3D效果"""
        # 创建阴影
        shadow = Image.new('RGBA', image.size, (0, 0, 0, 0))
        shadow_draw = ImageDraw.Draw(shadow)

        # 计算光源方向
        light_x = math.cos(math.radians(light_angle)) * intensity
        light_y = math.sin(math.radians(light_angle)) * intensity

        # 绘制阴影
        shadow_offset = self.shadow_offset
        shadow_draw.rectangle(
            [shadow_offset, shadow_offset, image.width, image.height],
            fill=(0, 0, 0, 100)
        )

        # 模糊阴影
        shadow = shadow.filter(ImageFilter.GaussianBlur(radius=5))

        # 创建高光
        highlight = Image.new('RGBA', image.size, (0, 0, 0, 0))
        highlight_draw = ImageDraw.Draw(highlight)

        # 绘制高光
        highlight_offset = -shadow_offset
        highlight_draw.rectangle(
            [highlight_offset, highlight_offset, image.width - shadow_offset*2, image.height - shadow_offset*2],
            fill=(255, 255, 255, 50)
        )

        # 模糊高光
        highlight = highlight.filter(ImageFilter.GaussianBlur(radius=3))

        # 合成图像
        result = Image.alpha_composite(shadow, image)
        result = Image.alpha_composite(result, highlight)

        return result

    def draw_3d_light(self, x, y, size):
        """绘制3D灯"""
        # 创建灯的图像
        image = Image.new('RGBA', (size*2, size*2), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)

        # 绘制灯
        if self.devices["light"]:
            # 灯开
            # 绘制灯泡
            draw.ellipse([size/2, size/2, size*1.5, size*1.5], fill=(255, 255, 0, 255), outline=(255, 165, 0, 255))

            # 绘制灯光效果
            for i in range(5):
                alpha = 150 - i * 30
                if alpha > 0:
                    draw.ellipse(
                        [size/2 - i*5, size/2 - i*5, size*1.5 + i*5, size*1.5 + i*5],
                        fill=(255, 255, 0, alpha),
                        outline=None
                    )
        else:
            # 灯关
            draw.ellipse([size/2, size/2, size*1.5, size*1.5], fill=(100, 100, 100, 255), outline=(50, 50, 50, 255))

        # 绘制灯座
        draw.rectangle([size*0.8, size*1.5, size*1.2, size*1.8], fill=(150, 150, 150, 255), outline=(100, 100, 100, 255))

        # 添加3D效果
        image = self.create_3d_effect(image)

        # 转换为Tkinter可用的格式
        tk_image = ImageTk.PhotoImage(image)

        # 缓存图像以防止垃圾回收
        self.device_icons["light"] = tk_image

        # 在画布上绘制图像
        self.room_canvas.create_image(x, y, image=tk_image)

        # 添加标签
        label_text = "灯光" if not self.devices["light"] else "灯光 (开启)"
        label_color = self.text_color if not self.devices["light"] else self.success_color

        self.room_canvas.create_text(
            x, y + size,
            text=label_text,
            font=self.device_font,
            fill=label_color
        )

    def draw_3d_tv(self, x, y, size):
        """绘制3D电视"""
        # 创建电视的图像
        image = Image.new('RGBA', (size*2, size*2), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)

        # 绘制电视
        if self.devices["tv"]:
            # 电视开
            # 绘制电视机身
            draw.rectangle([size/2, size/2, size*1.5, size*1.3], fill=(50, 50, 50, 255), outline=(30, 30, 30, 255))

            # 绘制屏幕
            draw.rectangle([size/2 + 5, size/2 + 5, size*1.5 - 5, size*1.3 - 5], fill=(0, 100, 200, 255), outline=None)

            # 绘制电视节目
            for i in range(3):
                draw.line(
                    [size/2 + 10, size/2 + 15 + i*15, size*1.5 - 10, size/2 + 15 + i*15],
                    fill=(255, 255, 255, 150),
                    width=2
                )
        else:
            # 电视关
            # 绘制电视机身
            draw.rectangle([size/2, size/2, size*1.5, size*1.3], fill=(50, 50, 50, 255), outline=(30, 30, 30, 255))

            # 绘制屏幕
            draw.rectangle([size/2 + 5, size/2 + 5, size*1.5 - 5, size*1.3 - 5], fill=(20, 20, 20, 255), outline=None)

        # 绘制电视底座
        draw.rectangle([size*0.8, size*1.3, size*1.2, size*1.5], fill=(30, 30, 30, 255), outline=None)

        # 添加3D效果
        image = self.create_3d_effect(image)

        # 转换为Tkinter可用的格式
        tk_image = ImageTk.PhotoImage(image)

        # 缓存图像以防止垃圾回收
        self.device_icons["tv"] = tk_image

        # 在画布上绘制图像
        self.room_canvas.create_image(x, y, image=tk_image)

        # 添加标签
        label_text = "电视" if not self.devices["tv"] else "电视 (开启)"
        label_color = self.text_color if not self.devices["tv"] else self.success_color

        self.room_canvas.create_text(
            x, y + size/2 + 10,
            text=label_text,
            font=self.device_font,
            fill=label_color
        )

    def draw_3d_ac(self, x, y, size):
        """绘制3D空调"""
        # 创建空调的图像
        image = Image.new('RGBA', (size*2, size*2), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)

        # 绘制空调 - 确保坐标正确
        if self.devices["ac"]:
            # 空调开
            # 绘制空调机身 - 水平放置
            draw.rectangle([size/2, size/2, size*1.5, size*0.9], fill=(240, 240, 240, 255), outline=(200, 200, 200, 255))

            # 绘制出风口
            draw.rectangle([size/2 + 5, size/2 + size*0.3, size*1.5 - 5, size/2 + size*0.4], fill=(220, 220, 220, 255), outline=None)

            # 绘制控制面板
            draw.rectangle([size*1.3, size/2 + 5, size*1.4, size/2 + 15], fill=(0, 200, 0, 255), outline=None)

            # 绘制冷风效果 - 使用简单的线条
            for i in range(3):
                y_pos = size*1.0 + i*10
                draw.line(
                    [size/2 + 10, y_pos, size*1.5 - 10, y_pos],
                    fill=(100, 200, 255, 150),
                    width=2
                )
        else:
            # 空调关
            # 绘制空调机身 - 水平放置
            draw.rectangle([size/2, size/2, size*1.5, size*0.9], fill=(220, 220, 220, 255), outline=(180, 180, 180, 255))

            # 绘制出风口
            draw.rectangle([size/2 + 5, size/2 + size*0.3, size*1.5 - 5, size/2 + size*0.4], fill=(200, 200, 200, 255), outline=None)

            # 绘制控制面板
            draw.rectangle([size*1.3, size/2 + 5, size*1.4, size/2 + 15], fill=(150, 150, 150, 255), outline=None)

        # 添加3D效果
        image = self.create_3d_effect(image)

        # 转换为Tkinter可用的格式
        tk_image = ImageTk.PhotoImage(image)

        # 缓存图像以防止垃圾回收
        self.device_icons["ac"] = tk_image

        # 在画布上绘制图像
        self.room_canvas.create_image(x, y, image=tk_image)

        # 添加标签
        label_text = "空调" if not self.devices["ac"] else "空调 (开启)"
        label_color = self.text_color if not self.devices["ac"] else self.success_color

        self.room_canvas.create_text(
            x, y + size/2,
            text=label_text,
            font=self.device_font,
            fill=label_color
        )

    def trim_silence(self, audio_data, threshold=0.02, frame_length=1024, hop_length=256):
        """裁剪音频中的静音部分"""
        # 计算音频的能量
        energy = librosa.feature.rms(y=audio_data, frame_length=frame_length, hop_length=hop_length)[0]

        # 找出能量大于阈值的帧
        frames = np.where(energy > threshold)[0]
        if len(frames) == 0:
            print("警告: 没有检测到有效音频，返回原始音频")
            return audio_data

        # 找出第一个和最后一个非静音帧
        start_frame = max(0, frames[0] - 5)  # 向前多取5帧，避免截断开头
        end_frame = min(len(energy) - 1, frames[-1] + 5)  # 向后多取5帧，避免截断结尾

        # 转换为样本索引
        start_sample = start_frame * hop_length
        end_sample = min(len(audio_data) - 1, (end_frame + 1) * hop_length)

        # 裁剪音频
        trimmed_audio = audio_data[start_sample:end_sample]

        # 确保裁剪后的音频不为空
        if len(trimmed_audio) < 100:
            print("警告: 裁剪后的音频太短，返回原始音频")
            return audio_data

        return trimmed_audio

    def log_system_event(self, level, module, message, details=None):
        """记录系统事件到数据库"""
        if self.db:
            try:
                self.db.add_system_log(level, module, message, details)
            except Exception as e:
                print(f"记录系统日志失败: {e}")

    def log_user_operation(self, operation_type, operation_details=None, result='SUCCESS', error_message=None):
        """记录用户操作到数据库"""
        if self.db:
            try:
                self.db.add_user_operation(operation_type, operation_details, result, error_message)
            except Exception as e:
                print(f"记录用户操作失败: {e}")

    def log_speech_recognition(self, source, predicted_class, command, confidence,
                              file_path=None, audio_length=None, audio_stats=None, success=True):
        """记录语音识别结果到数据库"""
        if self.db:
            try:
                record_id = self.db.add_speech_record(
                    source=source,
                    predicted_class=predicted_class,
                    command=command,
                    confidence=confidence,
                    file_path=file_path,
                    audio_length=audio_length,
                    audio_stats=json.dumps(audio_stats) if audio_stats else None,
                    success=success
                )
                return record_id
            except Exception as e:
                print(f"记录语音识别失败: {e}")
                return None
        return None

    def log_device_control(self, device_name, device_label, action, previous_state,
                          new_state, speech_record_id=None):
        """记录设备控制到数据库"""
        if self.db:
            try:
                self.db.add_device_record(
                    device_name=device_name,
                    device_label=device_label,
                    action=action,
                    previous_state=previous_state,
                    new_state=new_state,
                    triggered_by_speech=True,
                    speech_record_id=speech_record_id
                )
            except Exception as e:
                print(f"记录设备控制失败: {e}")

    def get_database_statistics(self):
        """获取数据库统计信息"""
        if not self.db:
            return None

        try:
            stats = {
                'recent_speech_records': self.db.get_recent_speech_records(5),
                'recent_device_records': self.db.get_recent_device_records(5),
                'device_statistics': self.db.get_device_statistics(),
                'recognition_accuracy': self.db.get_recognition_accuracy()
            }
            return stats
        except Exception as e:
            print(f"获取数据库统计失败: {e}")
            return None

    def cleanup(self):
        """清理资源"""
        print("清理资源...")

        # 记录系统关闭事件
        self.log_system_event('INFO', 'GUI', '系统关闭', '用户关闭智能家居语音控制系统')
        self.log_user_operation('关闭系统', '用户关闭智能家居语音控制系统')

        # 停止录音
        self.is_recording = False
        if self.recording_thread and self.recording_thread.is_alive():
            self.recording_thread.join(timeout=1.0)

        # 停止自动监听
        self.auto_listening = False
        if self.listening_thread and self.listening_thread.is_alive():
            self.listening_thread.join(timeout=1.0)

        # 关闭PyAudio
        if self.audio:
            self.audio.terminate()

        # 关闭数据库连接
        if self.db:
            try:
                self.db.close()
                print("数据库连接已关闭")
            except Exception as e:
                print(f"关闭数据库连接失败: {e}")

        print("资源清理完成")

if __name__ == "__main__":
    print("启动智能家居语音控制系统...")
    root = tk.Tk()
    app = SmartHomeGUI(root)

    # 添加窗口关闭事件处理
    def on_closing():
        app.cleanup()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    root.mainloop()
    print("GUI应用已关闭")
