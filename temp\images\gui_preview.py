#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
绘制智能家居GUI界面预览图
"""

import os
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib
import numpy as np
from PIL import Image, ImageDraw, ImageFont

# 确保能显示中文
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

# 创建一个PIL图像来模拟GUI界面
def create_gui_mockup():
    # 创建空白图像 (1200x800 像素)
    width, height = 1200, 800
    image = Image.new('RGB', (width, height), color='#2c3e50')
    draw = ImageDraw.Draw(image)
    
    # 尝试加载字体
    try:
        title_font = ImageFont.truetype('SimSun', 24)
        header_font = ImageFont.truetype('SimSun', 18)
        normal_font = ImageFont.truetype('SimSun', 14)
    except IOError:
        # 如果找不到字体，使用默认字体
        title_font = ImageFont.load_default()
        header_font = ImageFont.load_default()
        normal_font = ImageFont.load_default()
    
    # 顶部标题栏
    draw.rectangle([0, 0, width, 60], fill='#34495e')
    draw.text((width//2, 30), "智能家居语音控制系统", font=title_font, fill='white', anchor='mm')
    
    # 左侧控制面板
    control_panel_width = 300
    draw.rectangle([20, 80, control_panel_width + 20, 380], fill='#f5f5f5', outline='#7f8c8d')
    draw.text((control_panel_width//2 + 20, 100), "控制面板", font=header_font, fill='#2c3e50', anchor='mm')
    
    # 录音按钮
    button_y = 150
    draw.rectangle([60, button_y, 280, button_y + 40], fill='#e74c3c', outline='#c0392b')
    draw.text((170, button_y + 20), "开始录音", font=normal_font, fill='white', anchor='mm')
    
    # 上传按钮
    button_y += 60
    draw.rectangle([60, button_y, 280, button_y + 40], fill='#3498db', outline='#2980b9')
    draw.text((170, button_y + 20), "上传音频", font=normal_font, fill='white', anchor='mm')
    
    # 自动监听按钮
    button_y += 60
    draw.rectangle([60, button_y, 280, button_y + 40], fill='#2ecc71', outline='#27ae60')
    draw.text((170, button_y + 20), "自动监听模式", font=normal_font, fill='white', anchor='mm')
    
    # 设备状态显示
    status_x = 50
    status_y = 350
    draw.text((status_x, status_y), "设备状态:", font=header_font, fill='#2c3e50')
    
    # 各设备状态
    devices = ["灯光", "电视", "空调"]
    for i, device in enumerate(devices):
        draw.text((status_x, status_y + 30 + i*25), f"{device}: 关闭", font=normal_font, fill='#7f8c8d')
    
    # 中部房间显示区域
    room_x, room_y = 340, 80
    room_width, room_height = 820, 500
    draw.rectangle([room_x, room_y, room_x + room_width, room_y + room_height], fill='#ecf0f1', outline='#7f8c8d')
    
    # 绘制3D房间效果
    # 地板
    floor_points = [(room_x + 50, room_y + 400), (room_x + 770, room_y + 400), (room_x + 770, room_y + 300), (room_x + 50, room_y + 300)]
    draw.polygon(floor_points, fill='#bdc3c7')
    
    # 墙壁
    wall_points = [(room_x + 50, room_y + 100), (room_x + 770, room_y + 100), (room_x + 770, room_y + 300), (room_x + 50, room_y + 300)]
    draw.polygon(wall_points, fill='#ecf0f1', outline='#95a5a6')
    
    # 灯
    draw.ellipse([room_x + 380, room_y + 120, room_x + 440, room_y + 150], fill='#f1c40f')
    draw.line([room_x + 410, room_y + 120, room_x + 410, room_y + 100], fill='#7f8c8d', width=2)
    
    # 电视
    tv_x, tv_y = room_x + 600, room_y + 180
    draw.rectangle([tv_x, tv_y, tv_x + 120, tv_y + 80], fill='#34495e', outline='#2c3e50')
    draw.rectangle([tv_x + 10, tv_y + 10, tv_x + 110, tv_y + 70], fill='#2c3e50')
    
    # 空调
    ac_x, ac_y = room_x + 200, room_y + 120
    draw.rectangle([ac_x, ac_y, ac_x + 100, ac_y + 40], fill='white', outline='#95a5a6')
    
    # 波形显示区域
    wave_x, wave_y = room_x, room_y + room_height + 20
    wave_width, wave_height = room_width, 180
    draw.rectangle([wave_x, wave_y, wave_x + wave_width, wave_y + wave_height], fill='#f5f5f5', outline='#7f8c8d')
    draw.text((wave_x + wave_width//2, wave_y + 20), "音频波形", font=header_font, fill='#2c3e50', anchor='mm')
    
    # 模拟波形
    points = []
    x_start = wave_x + 20
    y_center = wave_y + wave_height//2
    for i in range(780):
        x = x_start + i
        # 创建一个模拟的音频波形
        if i < 200:  # 静音部分
            y = y_center + np.sin(i/10) * 5
        elif i < 600:  # 语音部分
            y = y_center + np.sin(i/8) * 40 * (0.5 + 0.5 * np.sin(i/80))
        else:  # 静音部分
            y = y_center + np.sin(i/10) * 5
        points.append((x, y))
    
    # 绘制波形线
    for i in range(len(points) - 1):
        draw.line([points[i], points[i+1]], fill='#3498db', width=2)
    
    # 识别结果显示
    draw.text((wave_x + 20, wave_y + wave_height - 30), "识别结果: 打开红灯", font=header_font, fill='#e74c3c')
    
    # 底部状态栏
    draw.rectangle([0, height - 30, width, height], fill='#34495e')
    draw.text((width//2, height - 15), "系统就绪", font=normal_font, fill='white', anchor='mm')
    
    return image

# 创建GUI模拟图
gui_image = create_gui_mockup()

# 保存图像
gui_image.save('temp/images/gui_preview.png')

print("GUI界面预览图已保存到 temp/images/gui_preview.png")

# 显示图像
plt.figure(figsize=(15, 10))
plt.imshow(np.array(gui_image))
plt.axis('off')
plt.savefig('temp/images/gui_preview_matplotlib.png', dpi=300, bbox_inches='tight')
plt.close() 