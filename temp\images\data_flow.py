#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
绘制哈萨克语语音识别系统的数据流程图
"""

import os
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib
import numpy as np

# 确保能显示中文
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

# 创建图表
plt.figure(figsize=(14, 8))
plt.title('哈萨克语语音识别系统数据流程图', fontsize=20)

# 定义颜色
colors = {
    'input': '#3498db',
    'process': '#2ecc71',
    'augment': '#9b59b6',
    'model': '#e74c3c',
    'output': '#f39c12',
    'validate': '#1abc9c'
}

# 创建箭头样式
arrow_props = dict(arrowstyle='->', connectionstyle='arc3,rad=0.1', linewidth=2)

# 绘制流程节点
def draw_node(x, y, width, height, title, details=None, color='#3498db'):
    rect = patches.FancyBboxPatch((x, y), width, height, 
                                 boxstyle=patches.BoxStyle("Round", pad=0.3),
                                 linewidth=2, edgecolor='black', facecolor=color, alpha=0.7)
    plt.gca().add_patch(rect)
    plt.text(x + width/2, y + height - 0.25, title, 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    if details:
        detail_y = y + height - 0.6
        for detail in details:
            detail_y -= 0.3
            plt.text(x + width/2, detail_y, detail, ha='center', va='center', fontsize=10)

# 添加连接线
def connect(start, end, label=None, color='black', style=arrow_props):
    plt.annotate('', xy=end, xytext=start, arrowprops=style)
    if label:
        mid_x = (start[0] + end[0]) / 2
        mid_y = (start[1] + end[1]) / 2
        plt.text(mid_x, mid_y, label, ha='center', va='center', fontsize=10,
                bbox=dict(facecolor='white', alpha=0.7))

# 流程节点
# 1. 音频输入
draw_node(1, 6, 2.5, 1.5, '音频输入', 
         ['麦克风录音', '音频文件上传'], colors['input'])

# 2. 音频预处理
draw_node(5, 6, 2.5, 1.5, '音频预处理', 
         ['降噪', '归一化', '静音去除'], colors['process'])

# 3. 特征提取
draw_node(9, 6, 2.5, 1.5, '特征提取', 
         ['MFCC特征 (n_mfcc=60)', 'Delta特征', 'Delta-Delta特征'], colors['process'])

# 4a. 数据增强(训练)
draw_node(5, 3.5, 2.5, 1.5, '数据增强(训练)', 
         ['时间移位', '速度变化', '添加噪声', 'SpecAugment'], colors['augment'])

# 5a. 特征处理(训练)
draw_node(9, 3.5, 2.5, 1.5, '特征处理(训练)', 
         ['填充/截断(长度250)', '归一化', '转换为训练格式'], colors['process'])

# 4b. 测试预处理
draw_node(1, 3.5, 2.5, 1.5, '特征处理(测试)', 
         ['填充/截断特征', '归一化'], colors['process'])

# 5b. 模型训练
draw_node(7, 1, 2.5, 1.5, '模型训练', 
         ['批量大小: 16', '学习率: 0.001', '训练轮数: 50', '交叉验证(5折)'], colors['model'])

# 6. 模型预测
draw_node(1, 1, 2.5, 1.5, '模型预测', 
         ['集成预测', '置信度阈值'], colors['model'])

# 7. 控制命令输出
draw_node(3, -1.5, 2.5, 1.5, '控制命令处理', 
         ['命令映射', '设备控制'], colors['output'])

# 8. 结果展示
draw_node(7, -1.5, 2.5, 1.5, '结果展示', 
         ['3D可视化', '音频波形', '命令显示'], colors['output'])

# 9. 性能验证
draw_node(11, 1, 2.5, 1.5, '性能验证', 
         ['准确率评估', '混淆矩阵', '集成优化'], colors['validate'])

# 连接各节点 - 训练流程
connect((2.25, 6), (5, 6.5), '原始音频')
connect((7.5, 6.5), (9, 6.5), '处理后音频')
connect((10.25, 6), (9, 4.25), '原始特征')
connect((6.25, 3.5), (9, 3.5), '增强音频')
connect((2.25, 5.5), (5, 5.5), '测试音频')
connect((7.5, 5.5), (1, 4.25), '测试特征')
connect((7.5, 3.5), (7, 2.5), '训练特征')
connect((8.5, 1), (11, 1), '训练模型')
connect((11, 1.75), (10.25, 3.5), '性能反馈')
connect((2.25, 3.5), (1, 2.5), '测试特征')
connect((2.25, 1), (3, -0.5), '识别结果')
connect((4.5, -0.5), (7, -0.75), '控制命令')
connect((7, -0.5), (9, 0.5), '界面反馈')

# 添加训练/测试标签
plt.text(12, 5, '训练流程', fontsize=18, fontweight='bold', color=colors['model'])
plt.text(0.5, 2.5, '测试流程', fontsize=18, fontweight='bold', color=colors['output'])

# 设置图表范围
plt.xlim(0, 13)
plt.ylim(-2.5, 8)
plt.axis('off')

# 保存图片
plt.tight_layout()
plt.savefig('temp/images/data_flow.png', dpi=300, bbox_inches='tight')
plt.close()

print("数据流程图已保存到 temp/images/data_flow.png") 