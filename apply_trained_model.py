#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
将训练好的随机森林模型应用到前端应用中
"""

import os
import sys
import numpy as np
import joblib
import json
import pickle
import librosa
from sklearn.ensemble import RandomForestClassifier

# 类别映射
CLASS_MAPPING = {
    '0001-kd': '打开灯',
    '0002-gd': '关闭灯',
    '0003-kds': '打开电视',
    '0004-gds': '关闭电视',
    '0005-kkt': '打开空调'
}

def extract_mfcc(audio_data, sr, n_mfcc=13):
    """
    从音频数据中提取MFCC特征

    参数:
        audio_data (numpy.ndarray): 音频数据
        sr (int): 采样率
        n_mfcc (int): MFCC系数数量

    返回:
        numpy.ndarray: MFCC特征
    """
    try:
        # 确保采样率为16000
        if sr != 16000:
            audio_data = librosa.resample(audio_data, orig_sr=sr, target_sr=16000)
            sr = 16000

        # 提取MFCC特征
        mfccs = librosa.feature.mfcc(y=audio_data, sr=sr, n_mfcc=n_mfcc)

        # 计算MFCC特征的统计量
        mfccs_mean = np.mean(mfccs, axis=1)
        mfccs_std = np.std(mfccs, axis=1)
        mfccs_max = np.max(mfccs, axis=1)
        mfccs_min = np.min(mfccs, axis=1)

        # 组合特征
        features = np.concatenate((mfccs_mean, mfccs_std, mfccs_max, mfccs_min))

        return features

    except Exception as e:
        print(f"提取特征失败: {e}")
        return None

def create_label_map():
    """
    创建标签映射文件

    返回:
        dict: 标签映射
    """
    # 创建标签映射
    label_map = {
        "index_to_label": {},
        "label_to_index": {}
    }

    # 为每个类别分配索引
    for i, (class_code, class_name) in enumerate(CLASS_MAPPING.items()):
        label_map["index_to_label"][str(i)] = class_name
        label_map["label_to_index"][class_name] = i

    # 保存标签映射
    with open('models/label_map.json', 'w', encoding='utf-8') as f:
        json.dump(label_map, f, ensure_ascii=False, indent=4)

    print(f"标签映射已保存到 models/label_map.json")
    return label_map

def save_feature_params():
    """
    保存特征参数
    """
    # 特征参数
    feature_params = {
        'n_mfcc': 13,
        'sample_rate': 16000,
        'max_length': 100
    }

    # 保存特征参数
    with open('models/feature_params.pkl', 'wb') as f:
        pickle.dump(feature_params, f)

    print(f"特征参数已保存到 models/feature_params.pkl")

# 创建适配器类，将随机森林模型转换为Keras模型格式
class RFModelAdapter:
    def __init__(self, rf_model, label_map):
        self.rf_model = rf_model
        self.label_map = label_map
        self.index_to_label = {int(k): v for k, v in label_map["index_to_label"].items()}
        self.label_to_index = label_map["label_to_index"]

    def predict(self, X):
        """
        预测函数，模拟Keras模型的predict方法

        参数:
            X (numpy.ndarray): 输入特征

        返回:
            numpy.ndarray: 预测概率
        """
        # 获取预测概率
        proba = self.rf_model.predict_proba(X)
        return proba

def main():
    """主函数"""
    print("将训练好的随机森林模型应用到前端应用中...")

    # 检查模型文件是否存在
    rf_model_path = 'models/kazakh_speech_rf_model.joblib'
    if not os.path.exists(rf_model_path):
        print(f"错误: 模型文件不存在: {rf_model_path}")
        sys.exit(1)

    # 加载随机森林模型
    try:
        rf_model = joblib.load(rf_model_path)
        print(f"成功加载随机森林模型: {rf_model_path}")
    except Exception as e:
        print(f"加载模型失败: {e}")
        sys.exit(1)

    # 创建标签映射
    label_map = create_label_map()

    # 保存特征参数
    save_feature_params()

    # 创建适配器实例
    adapter = RFModelAdapter(rf_model, label_map)

    # 保存适配器
    with open('models/rf_model_adapter.pkl', 'wb') as f:
        pickle.dump(adapter, f)

    print("随机森林模型适配器已保存到 models/rf_model_adapter.pkl")
    print("现在可以在前端应用中使用训练好的随机森林模型了！")

if __name__ == "__main__":
    main()
