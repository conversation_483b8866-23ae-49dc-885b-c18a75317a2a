#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
纯Keras实现的哈萨克语语音识别模型
"""

import os
import numpy as np
import keras
from keras import layers, models
import librosa
import pickle
import json

class KazakhSpeechModel:
    """哈萨克语语音识别模型类 - 纯Keras实现"""
    
    def __init__(self):
        """初始化模型"""
        self.model = None
        self.feature_params = None
        self.labels = {
            0: "关闭灯",
            1: "关闭电视", 
            2: "打开灯",
            3: "打开电视",
            4: "打开空调"
        }
        self.actions = {
            "打开灯": ("light", True),
            "关闭灯": ("light", False),
            "打开电视": ("tv", True),
            "关闭电视": ("tv", False),
            "打开空调": ("ac", True),
            "关闭空调": ("ac", False)
        }
    
    def load_model(self, model_path="models/kazakh_speech_model.keras", params_path="models/feature_params.pkl", label_map_path="models/label_map.json"):
        """
        加载模型和参数
        
        参数:
            model_path (str): 模型文件路径
            params_path (str): 特征参数文件路径
            label_map_path (str): 标签映射文件路径
        
        返回:
            bool: 是否成功加载
        """
        try:
            # 加载特征参数
            with open(params_path, 'rb') as f:
                self.feature_params = pickle.load(f)
            print(f"已加载特征参数: {self.feature_params}")
            
            # 加载标签映射
            if os.path.exists(label_map_path):
                with open(label_map_path, 'r', encoding='utf-8') as f:
                    label_map = json.load(f)
                    self.labels = {int(k): v for k, v in label_map['index_to_label'].items()}
                print(f"已加载标签映射: {self.labels}")
            
            # 加载模型
            self.model = keras.models.load_model(model_path)
            print(f"已加载模型: {model_path}")
            
            return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False
    
    def extract_mfcc_features(self, audio, sample_rate):
        """
        提取MFCC特征
        
        参数:
            audio (numpy.ndarray): 音频数据
            sample_rate (int): 采样率
            
        返回:
            numpy.ndarray: MFCC特征
        """
        # 确保采样率一致
        if sample_rate != self.feature_params.get('sample_rate', 16000):
            audio = librosa.resample(audio, orig_sr=sample_rate, target_sr=self.feature_params.get('sample_rate', 16000))
        
        # 提取MFCC特征
        n_mfcc = self.feature_params.get('n_mfcc', 13)
        mfcc = librosa.feature.mfcc(y=audio, sr=self.feature_params.get('sample_rate', 16000), n_mfcc=n_mfcc)
        
        # 标准化特征
        mfcc = (mfcc - np.mean(mfcc)) / np.std(mfcc)
        
        return mfcc
    
    def pad_or_truncate(self, mfcc, max_length):
        """
        填充或截断MFCC特征
        
        参数:
            mfcc (numpy.ndarray): MFCC特征
            max_length (int): 最大长度
            
        返回:
            numpy.ndarray: 处理后的MFCC特征
        """
        if mfcc.shape[1] > max_length:
            # 截断
            return mfcc[:, :max_length]
        elif mfcc.shape[1] < max_length:
            # 填充
            pad_width = ((0, 0), (0, max_length - mfcc.shape[1]))
            return np.pad(mfcc, pad_width, mode='constant')
        else:
            return mfcc
    
    def preprocess_audio(self, audio_data, sample_rate):
        """
        预处理音频数据
        
        参数:
            audio_data (numpy.ndarray): 音频数据
            sample_rate (int): 采样率
            
        返回:
            numpy.ndarray: 处理后的特征
        """
        # 提取MFCC特征
        mfcc_features = self.extract_mfcc_features(audio_data, sample_rate)
        
        # 填充或截断特征
        max_length = self.feature_params.get('max_length', 100)
        padded_features = self.pad_or_truncate(mfcc_features, max_length)
        
        # 重塑特征以适应CNN输入
        features = padded_features.reshape(1, padded_features.shape[0], padded_features.shape[1], 1)
        
        return features
    
    def predict(self, audio_data, sample_rate):
        """
        预测音频数据的类别
        
        参数:
            audio_data (numpy.ndarray): 音频数据
            sample_rate (int): 采样率
            
        返回:
            str: 预测的命令
            float: 置信度
        """
        if self.model is None:
            print("错误: 模型未加载")
            return None, 0.0
        
        try:
            # 预处理音频
            features = self.preprocess_audio(audio_data, sample_rate)
            
            # 预测
            predictions = self.model.predict(features, verbose=0)
            predicted_class = np.argmax(predictions[0])
            confidence = predictions[0][predicted_class]
            
            # 获取命令
            command = self.labels.get(predicted_class, "未知命令")
            
            print(f"预测类别: {predicted_class}, 命令: {command}, 置信度: {confidence:.2f}")
            return command, confidence
            
        except Exception as e:
            print(f"预测出错: {e}")
            return None, 0.0
    
    def get_action(self, command):
        """
        获取命令对应的动作
        
        参数:
            command (str): 命令
            
        返回:
            tuple: (设备, 状态)
        """
        return self.actions.get(command, (None, None))
