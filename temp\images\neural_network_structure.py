#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
绘制Transformer神经网络结构图
"""

import os
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib
import numpy as np

# 确保能显示中文
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

# 创建图表
plt.figure(figsize=(14, 10))
plt.title('哈萨克语识别Transformer神经网络结构', fontsize=20)

# 定义颜色方案
colors = {
    'input': '#3498db',
    'embedding': '#2ecc71',
    'attention': '#9b59b6',
    'ffn': '#e74c3c',
    'output': '#f39c12',
    'arrow': '#7f8c8d',
    'layernorm': '#1abc9c'
}

# 绘制模块框
def draw_box(x, y, width, height, title, color, alpha=0.7):
    rect = patches.Rectangle((x, y), width, height, linewidth=2, 
                           edgecolor='black', facecolor=color, alpha=alpha)
    plt.gca().add_patch(rect)
    plt.text(x + width/2, y + height/2, title, 
           ha='center', va='center', fontsize=12, fontweight='bold')
    return (x + width/2, y)

# 绘制连接箭头
def draw_arrow(start, end, label=None, color='#7f8c8d'):
    plt.arrow(start[0], start[1], end[0] - start[0], end[1] - start[1],
             head_width=0.15, head_length=0.2, fc=color, ec=color, linewidth=2)
    if label:
        mid_x = (start[0] + end[0]) / 2
        mid_y = (start[1] + end[1]) / 2 + 0.1
        plt.text(mid_x, mid_y, label, ha='center', va='center', fontsize=10)

# 绘制输入层
input_box = draw_box(2, 9, 6, 0.8, '输入: MFCC特征 [batch_size, time_steps, features]', colors['input'])

# 绘制卷积嵌入层
conv_box = draw_box(2, 7.5, 6, 0.8, '卷积嵌入层: Conv1D(64) → Conv1D(128) → Conv1D(embed_dim)', colors['embedding'])

# 绘制位置编码层
pos_box = draw_box(2, 6, 6, 0.8, '位置编码层: PositionalEncoding', colors['embedding'], 0.6)

# 绘制Transformer块
def draw_transformer_block(y_pos, block_num):
    # Transformer块
    block_box = draw_box(1.5, y_pos, 7, 2.2, f'Transformer块 {block_num}', '#ecf0f1', 0.3)
    
    # 多头注意力
    attn_box = draw_box(2, y_pos + 1.4, 3, 0.6, f'多头注意力 ({num_heads}头)', colors['attention'])
    
    # 层归一化1
    norm1_box = draw_box(6, y_pos + 1.4, 2, 0.6, '层归一化', colors['layernorm'])
    
    # 前馈网络
    ffn_box = draw_box(2, y_pos + 0.2, 3, 0.6, f'前馈网络 (FFN)', colors['ffn'])
    
    # 层归一化2
    norm2_box = draw_box(6, y_pos + 0.2, 2, 0.6, '层归一化', colors['layernorm'])
    
    # 内部连接
    plt.arrow(3.5, y_pos + 1.4, 2.5, 0, head_width=0.1, head_length=0.1, 
             fc=colors['arrow'], ec=colors['arrow'])
    plt.arrow(3.5, y_pos + 0.2, 2.5, 0, head_width=0.1, head_length=0.1, 
             fc=colors['arrow'], ec=colors['arrow'])
    
    # 残差连接
    plt.plot([5, 5, 3.5, 3.5], [y_pos + 2.2, y_pos + 2.3, y_pos + 2.3, y_pos + 2.0], 
            'k--', color=colors['arrow'])
    plt.plot([5, 5, 3.5, 3.5], [y_pos + 0.8, y_pos + 0.9, y_pos + 0.9, y_pos + 0.8], 
            'k--', color=colors['arrow'])
    
    return block_box

# Transformer参数
num_blocks = 6
num_heads = 8
embed_dim = 128
ff_dim = 256

# 绘制多个Transformer块
transformer_blocks = []
current_y = 5
for i in range(3):  # 只画3个块代表6个
    block = draw_transformer_block(current_y - 2 * i, i+1)
    transformer_blocks.append(block)
    
# 添加省略号表示更多块
plt.text(5, current_y - 4.5, "...", fontsize=30, ha='center')

# 绘制全局平均池化层
pooling_box = draw_box(2, 0.6, 6, 0.8, '全局平均池化 + 残差连接', colors['layernorm'])

# 绘制输出层
output_box = draw_box(2, -0.8, 6, 0.8, '全连接层 + Softmax输出', colors['output'])

# 连接各层
draw_arrow(input_box, conv_box)
draw_arrow(conv_box, pos_box)
draw_arrow(pos_box, transformer_blocks[0])

for i in range(len(transformer_blocks) - 1):
    if i < len(transformer_blocks) - 1:
        draw_arrow(transformer_blocks[i], transformer_blocks[i+1])

# 连接最后的Transformer块到省略号
last_block_pos = (transformer_blocks[-1][0], transformer_blocks[-1][1] - 2.2)
plt.arrow(last_block_pos[0], last_block_pos[1], 0, -0.5,
         head_width=0.15, head_length=0.2, fc=colors['arrow'], ec=colors['arrow'])

# 连接省略号到池化层
plt.arrow(5, current_y - 5.2, 0, -1.5,
         head_width=0.15, head_length=0.2, fc=colors['arrow'], ec=colors['arrow'])

draw_arrow((5, current_y - 7), pooling_box)
draw_arrow(pooling_box, output_box)

# 添加模型参数说明
params_text = f"""
模型参数:
- 嵌入维度: {embed_dim}
- 多头注意力头数: {num_heads}
- 前馈网络维度: {ff_dim}
- Transformer块数量: {num_blocks}
- 学习率: 0.001
- 优化器: Adam
- 批量大小: 16
- 训练轮数: 50
- 损失函数: 交叉熵
"""

# 在右侧添加参数说明
plt.text(10, 5, params_text, fontsize=12, bbox=dict(facecolor='white', alpha=0.7))

# 设置图表范围
plt.xlim(0, 14)
plt.ylim(-1.5, 10)
plt.axis('off')

# 保存图片
plt.tight_layout()
plt.savefig('temp/images/transformer_structure.png', dpi=300, bbox_inches='tight')
plt.close()

print("神经网络结构图已保存到 temp/images/transformer_structure.png") 