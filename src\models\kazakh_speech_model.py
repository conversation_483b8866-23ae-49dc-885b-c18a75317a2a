#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
哈萨克语语音命令识别模型
"""

import os
import numpy as np
import tensorflow as tf
import librosa
import pickle
import json
from src.models.transformer_model import KazakhSpeechTransformer, TransformerBlock, PositionalEncoding, ConvEmbedding

class KazakhSpeechModel:
    """哈萨克语语音命令识别模型"""
    
    def __init__(self):
        """初始化模型"""
        self.model = None
        self.feature_params = None
        self.is_ensemble = False
        self.custom_objects = {
            'TransformerBlock': TransformerBlock,
            'PositionalEncoding': PositionalEncoding,
            'ConvEmbedding': ConvEmbedding
        }
        self.labels = {
            0: "关闭灯",
            1: "关闭电视", 
            2: "打开灯",
            3: "打开电视",
            4: "打开空调"
        }
        self.actions = {
            "打开灯": ("light", True),
            "关闭灯": ("light", False),
            "打开电视": ("tv", True),
            "关闭电视": ("tv", False),
            "打开空调": ("ac", True),
            "关闭空调": ("ac", False)
        }
        
    def load_model(self, model_path="models/kazakh_transformer_model.h5", params_path="models/feature_params.pkl", label_map_path="models/label_map.json"):
        """
        加载模型和参数
        
        参数:
            model_path (str): 模型文件路径
            params_path (str): 特征参数文件路径
            label_map_path (str): 标签映射文件路径
        
        返回:
            bool: 是否成功加载
        """
        try:
            # 判断是否是集成模型
            self.is_ensemble = "ensemble" in model_path.lower()
            
            # 如果是集成模型，使用fold_1的特征参数
            if self.is_ensemble:
                ensemble_params_path = params_path.replace("feature_params.pkl", "feature_params_fold_1.pkl")
                if os.path.exists(ensemble_params_path):
                    params_path = ensemble_params_path
                    print(f"使用集成模型参数: {params_path}")
                
                # 同样使用fold_1的标签映射
                ensemble_label_map = label_map_path.replace("label_map.json", "label_map_fold_1.json")
                if os.path.exists(ensemble_label_map):
                    label_map_path = ensemble_label_map
                    print(f"使用集成模型标签映射: {label_map_path}")
            
            # 加载特征参数
            with open(params_path, 'rb') as f:
                self.feature_params = pickle.load(f)
            print(f"已加载特征参数: {self.feature_params}")
            
            # 加载标签映射
            if os.path.exists(label_map_path):
                with open(label_map_path, 'r', encoding='utf-8') as f:
                    label_map = json.load(f)
                    self.labels = {int(k): v for k, v in label_map['index_to_label'].items()}
                print(f"已加载标签映射: {self.labels}")
            
            # 加载模型
            self.model = tf.keras.models.load_model(
                model_path,
                custom_objects=self.custom_objects
            )
            print(f"已加载模型: {model_path} {'(集成模型)' if self.is_ensemble else ''}")
            
            return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False

    def preprocess_audio(self, audio_data, sample_rate):
        """
        预处理音频数据
        
        参数:
            audio_data (numpy.ndarray): 音频数据
            sample_rate (int): 采样率
            
        返回:
            numpy.ndarray: 处理后的特征
        """
        from src.features.mfcc_extractor import MFCCExtractor
        
        # 确保采样率一致
        if sample_rate != self.feature_params.get('sample_rate', 16000):
            audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=self.feature_params.get('sample_rate', 16000))
        
        # 提取MFCC特征
        feature_extractor = MFCCExtractor(
            sample_rate=self.feature_params.get('sample_rate', 16000),
            n_mfcc=self.feature_params.get('n_mfcc', 40),
            n_fft=self.feature_params.get('n_fft', 512),
            hop_length=self.feature_params.get('hop_length', 160)
        )
        features = feature_extractor.extract(audio_data)
        
        # 处理特征形状
        max_time_dim = self.feature_params.get('max_time_dim', 200)
        
        # 如果时间维度超出，进行截断
        if features.shape[1] > max_time_dim:
            features = features[:, :max_time_dim]
        # 如果时间维度不足，进行填充    
        elif features.shape[1] < max_time_dim:
            pad_width = ((0, 0), (0, max_time_dim - features.shape[1]))
            features = np.pad(features, pad_width, mode='constant')
            
        # 转置特征以适应Transformer输入格式 [batch, time, features]
        features = np.transpose(features, (1, 0))
        features = np.expand_dims(features, axis=0)  # 添加批次维度
        
        return features
        
    def predict(self, audio_data, sample_rate):
        """
        预测音频数据的类别
        
        参数:
            audio_data (numpy.ndarray): 音频数据
            sample_rate (int): 采样率
            
        返回:
            str: 预测的命令
            float: 置信度
        """
        if self.model is None:
            print("错误: 模型未加载")
            return None, 0.0
        
        try:
            # 预处理音频
            features = self.preprocess_audio(audio_data, sample_rate)
            
            # 预测
            predictions = self.model.predict(features, verbose=0)
            predicted_class = np.argmax(predictions[0])
            confidence = predictions[0][predicted_class]
            
            # 处理集成模型的预测结果 - 集成模型的置信度较高
            if self.is_ensemble:
                # 集成模型通常有更高的置信度门槛
                confidence_threshold = 0.6
                if confidence < confidence_threshold:
                    print(f"集成模型置信度 {confidence:.2f} 低于阈值 {confidence_threshold}")
                    
                    # 仍然返回最高置信度的预测，但标记置信度较低
                    command = self.labels.get(predicted_class, "未知命令")
                    return command, confidence
            
            # 获取命令
            command = self.labels.get(predicted_class, "未知命令")
            
            print(f"预测类别: {predicted_class}, 命令: {command}, 置信度: {confidence:.2f}")
            return command, confidence
            
        except Exception as e:
            print(f"预测出错: {e}")
            return None, 0.0
            
    def get_action(self, command):
        """
        获取命令对应的动作
        
        参数:
            command (str): 命令
            
        返回:
            tuple: (设备, 状态)
        """
        return self.actions.get(command, (None, None))
